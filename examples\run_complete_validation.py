#!/usr/bin/env python3
"""
AKID-NSGA2 实验执行脚本
自动执行完整的实验计划
"""

import os
import json
import time
from tests_suite.test_akid_unit_tests import run_akid_unit_tests
from tests_suite.test_akid_performance_comparison import run_performance_comparison_test
from tests_suite.test_akid_integration import main as run_integration_test

def main():
    print("="*80)
    print("AKID-NSGA2 完整实验验证")
    print("="*80)
    print("按照实施计划第6节要求执行完整验证...")
    print()
    
    results = {}
    
    # 阶段1：单元测试
    print("阶段1：执行单元测试...")
    unit_test_success, unit_test_report = run_akid_unit_tests()
    results['unit_tests'] = {
        'success': unit_test_success,
        'report': unit_test_report
    }
    
    # 阶段2：集成测试
    print("\n阶段2：执行集成测试...")
    try:
        run_integration_test()
        results['integration_tests'] = {'success': True}
    except Exception as e:
        results['integration_tests'] = {'success': False, 'error': str(e)}
    
    # 阶段3：性能对比测试
    print("\n阶段3：执行性能对比测试...")
    perf_test_success = run_performance_comparison_test()
    results['performance_tests'] = {'success': perf_test_success}
    
    # 生成最终报告
    final_report = {
        'experiment_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'overall_success': all([
            results['unit_tests']['success'],
            results['integration_tests']['success'],
            results['performance_tests']['success']
        ]),
        'stage_results': results
    }
    
    # 保存最终报告
    os.makedirs('test_results/final_report/', exist_ok=True)
    with open('test_results/final_report/complete_validation_report.json', 'w') as f:
        json.dump(final_report, f, indent=2)
    
    # 输出总结
    print("\n" + "="*80)
    print("实验验证总结")
    print("="*80)
    
    if final_report['overall_success']:
        print("✅ 所有验证测试通过！")
        print("AKID-NSGA2实施计划第6节验证完成。")
    else:
        print("❌ 部分验证测试失败！")
        print("请检查详细报告：test_results/final_report/")
    
    print("="*80)

if __name__ == "__main__":
    main()
