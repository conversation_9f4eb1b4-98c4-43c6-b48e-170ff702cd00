# AKID-NSGA2 使用指南

## 概述

AKID-NSGA2（Adaptive Knowledge Injection Dynamic NSGA-II）是一个增强版的NSGA-II算法，专门为机场滑行路径优化问题设计。它通过动态知识管理、自适应注入机制和关键路径识别技术，显著提升算法的收敛性能和解的质量。

## 快速开始

### 1. 基本运行

修改 `main_global.py` 中的配置并运行：

```python
# 启用AKID增强
USE_AKID_ENHANCEMENT = True
AKID_CONFIG_NAME = 'default'

# 运行程序
python main_global.py
```

### 2. 算法对比

```python
# 标准NSGA-II
USE_AKID_ENHANCEMENT = False

# AKID-NSGA2
USE_AKID_ENHANCEMENT = True
```

### 3. 快速测试

运行集成测试验证功能：

```bash
python test_akid_integration.py
```

## 配置选项

### 预设配置

#### 1. 默认配置 (`default`)
- **适用场景**: 一般优化问题
- **知识库容量**: 100
- **多样性阈值**: 0.3
- **注入比例**: 5-20%

#### 2. 保守配置 (`conservative`)
- **适用场景**: 对稳定性要求较高的场景
- **知识库容量**: 50
- **多样性阈值**: 0.2
- **注入比例**: 3-10%

#### 3. 激进配置 (`aggressive`)
- **适用场景**: 需要快速收敛的场景
- **知识库容量**: 150
- **多样性阈值**: 0.4
- **注入比例**: 10-30%

### 自定义配置

```python
AKID_CONFIG_NAME = 'custom'
CUSTOM_AKID_CONFIG = {
    'dkb_config': {
        'capacity': 100,                # 知识库容量
        'elite_ratio': 0.3,            # 精英解比例
        'quality_weight': 0.7,         # 质量权重
        'auto_cleanup': True           # 自动清理
    },
    'dcmm_config': {
        'window_size': 5,                    # 监控窗口
        'stagnation_threshold': 3,           # 停滞阈值
        'diversity_threshold': 0.3,          # 多样性阈值
        'convergence_threshold': 0.001       # 收敛阈值
    },
    'akim_config': {
        'injection_ratio_range': (0.05, 0.2), # 注入比例范围
        'adaptation_learning_rate': 0.1,      # 学习率
        'success_threshold': 0.7               # 成功率阈值
    },
    'key_segment_config': {
        'enable_identification': True,      # 启用路径识别
        'min_length': 3,                   # 最小路径长度
        'min_frequency': 3,                # 最小频率
        'quality_threshold': 0.5           # 质量阈值
    }
}
```

## 核心模块说明

### 1. 动态知识库 (DKB)

**功能**: 存储和管理优秀解
- 自动容量管理
- 质量评估和排序
- 重复解检测
- 支持导入导出

**关键参数**:
- `capacity`: 知识库最大容量
- `elite_ratio`: 精英解比例
- `quality_weight`: 质量权重

### 2. 多样性监控 (DCMM)

**功能**: 监控种群状态
- 实时多样性计算
- 收敛趋势分析
- 停滞检测
- 注入时机判断

**关键参数**:
- `window_size`: 监控窗口大小
- `diversity_threshold`: 多样性阈值
- `stagnation_threshold`: 停滞检测阈值

### 3. 知识注入 (AKIM)

**功能**: 自适应知识注入
- 智能注入时机选择
- 自适应注入数量
- 成功率评估
- 策略权重调整

**关键参数**:
- `injection_ratio_range`: 注入比例范围
- `adaptation_learning_rate`: 自适应学习率
- `success_threshold`: 成功率阈值

### 4. 关键路径识别 (KSPI)

**功能**: 识别和利用关键子路径
- 高频路径段提取
- 质量评估
- 路径增强应用

**关键参数**:
- `min_length`: 最小路径长度
- `min_frequency`: 最小出现频率
- `similarity_threshold`: 相似度阈值

## 输出和结果分析

### 1. 标准输出

```
• 优化算法: AKID-NSGA2增强算法
• AKID配置: default
• 知识库容量: 100
• 多样性阈值: 0.3
• 注入比例范围: (0.05, 0.2)
```

### 2. 运行时信息

```
运行 1 完成 - 帕累托前沿大小: 25, 最小时间: 145.67, 最小燃油: 89.34
  AKID统计 - 知识注入次数: 8, 平均多样性: 0.2847
```

### 3. 最终统计

```
• AKID增强统计:
  - 平均知识注入次数: 7.3
  - 平均多样性指标: 0.2956
  - AKID综合报告: results/akid_nsga2/akid_summary_report.json
```

### 4. 文件输出

#### 基本结果文件
- `PF_run_X.npy`: 帕累托前沿
- `PS_run_X.npy`: 帕累托集
- `PF_PS_run_X.npz`: 所有代的结果

#### AKID特定文件
- `AKID_report_run_X.json`: 详细AKID报告
- `knowledge_base_run_X.json`: 知识库数据
- `key_segments_run_X.json`: 关键路径段
- `akid_summary_report.json`: 综合报告

## 性能调优建议

### 1. 根据问题规模调整

**小规模问题** (飞机数 < 10):
```python
AKID_CONFIG_NAME = 'conservative'
```

**中等规模问题** (飞机数 10-20):
```python
AKID_CONFIG_NAME = 'default'
```

**大规模问题** (飞机数 > 20):
```python
AKID_CONFIG_NAME = 'aggressive'
```

### 2. 根据计算资源调整

**资源有限**:
```python
CUSTOM_AKID_CONFIG = {
    'dkb_config': {'capacity': 50},
    'dcmm_config': {'window_size': 3},
    'monitoring_config': {'enable_detailed_logging': False}
}
```

**资源充足**:
```python
CUSTOM_AKID_CONFIG = {
    'dkb_config': {'capacity': 200},
    'dcmm_config': {'window_size': 10},
    'monitoring_config': {'enable_detailed_logging': True}
}
```

### 3. 根据收敛要求调整

**快速收敛**:
```python
CUSTOM_AKID_CONFIG = {
    'dcmm_config': {
        'stagnation_threshold': 2,
        'diversity_threshold': 0.4
    },
    'akim_config': {
        'injection_ratio_range': (0.1, 0.3)
    }
}
```

**高质量解**:
```python
CUSTOM_AKID_CONFIG = {
    'dcmm_config': {
        'stagnation_threshold': 5,
        'diversity_threshold': 0.2
    },
    'akim_config': {
        'injection_ratio_range': (0.03, 0.1)
    }
}
```

## 故障排除

### 1. 常见错误

#### 模块导入错误
```
ImportError: No module named 'akid_core'
```
**解决方案**: 确保所有AKID模块文件存在于工作目录

#### 配置验证失败
```
配置验证: 失败 - Invalid configuration
```
**解决方案**: 检查自定义配置的参数类型和取值范围

#### 内存不足
```
MemoryError: Unable to allocate memory
```
**解决方案**: 减少知识库容量或监控窗口大小

### 2. 性能问题

#### 运行速度慢
- 减少知识库容量
- 关闭详细日志记录
- 使用快速超体积计算方法

#### 收敛效果差
- 增加知识库容量
- 调整多样性阈值
- 修改注入比例范围

### 3. 调试方法

#### 启用详细日志
```python
CUSTOM_AKID_CONFIG = {
    'monitoring_config': {
        'enable_detailed_logging': True,
        'save_injection_history': True
    }
}
```

#### 运行测试脚本
```bash
python test_akid_integration.py
```

## 实验对比指南

### 1. 对比实验设置

```python
# 实验1: 标准NSGA-II
USE_AKID_ENHANCEMENT = False

# 实验2: AKID-NSGA2保守配置
USE_AKID_ENHANCEMENT = True
AKID_CONFIG_NAME = 'conservative'

# 实验3: AKID-NSGA2默认配置
USE_AKID_ENHANCEMENT = True
AKID_CONFIG_NAME = 'default'

# 实验4: AKID-NSGA2激进配置
USE_AKID_ENHANCEMENT = True
AKID_CONFIG_NAME = 'aggressive'
```

### 2. 评估指标

- **收敛性**: 帕累托前沿质量
- **多样性**: 解的分布均匀性
- **效率**: 计算时间
- **稳定性**: 多次运行的一致性

### 3. 结果分析

查看综合报告文件进行详细分析：
- `akid_summary_report.json`
- 各种统计图表
- 知识注入历史

## 最佳实践

1. **首次使用**: 从默认配置开始
2. **参数调优**: 逐步调整单个参数
3. **性能监控**: 关注内存和时间消耗
4. **结果验证**: 使用多次独立运行
5. **配置记录**: 保存有效的配置设置

## 技术支持

如果遇到问题，请：
1. 运行集成测试脚本
2. 检查错误日志文件
3. 参考故障排除指南
4. 查看AKID报告文件 