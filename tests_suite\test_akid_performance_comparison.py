"""
AKID-NSGA2性能对比测试模块

实现验证和测试计划第6.2节：性能对比测试
1. 收敛性对比：AKID-NSGA2 vs 标准NSGA-II
2. 多样性对比：帕累托前沿分布质量  
3. 计算效率对比：运行时间和内存使用

按照AKID_NSGA2_Implementation_Plan.md第6节要求实施
"""

import pandas as pd
import networkx as nx
import numpy as np
import os
import time
import json
import psutil
import matplotlib.pyplot as plt
from typing import List, Dict, Tuple, Any
import threading
import tracemalloc

# 导入算法模块
from nsga2_core.algorithm import MARMT_RK_global, MARMT_RK_global_with_akid
from config import get_akid_config


class PerformanceMetrics:
    """性能指标计算类"""
    
    @staticmethod
    def calculate_hypervolume(pareto_front: List[List[float]], reference_point: List[float] = None) -> float:
        """
        计算超体积指标
        
        参数:
            pareto_front: 帕累托前沿点集
            reference_point: 参考点
            
        返回:
            超体积值
        """
        if not pareto_front:
            return 0.0
            
        pf_array = np.array(pareto_front)
        
        # 如果没有指定参考点，使用最大值+10%作为参考点
        if reference_point is None:
            max_values = np.max(pf_array, axis=0)
            reference_point = max_values * 1.1
            
        # 简化的超体积计算（2D情况）
        if pf_array.shape[1] == 2:
            # 按第一个目标排序
            sorted_pf = pf_array[np.argsort(pf_array[:, 0])]
            
            volume = 0.0
            prev_x = 0.0
            
            for point in sorted_pf:
                x, y = point
                if x > prev_x:
                    width = x - prev_x
                    height = reference_point[1] - y
                    volume += width * height
                    prev_x = x
                    
            return volume
        else:
            # 多维情况的简化计算
            volumes = []
            for point in pf_array:
                volume = np.prod([ref - obj for ref, obj in zip(reference_point, point)])
                volumes.append(max(0, volume))
            return sum(volumes)
    
    @staticmethod
    def calculate_spacing(pareto_front: List[List[float]]) -> float:
        """
        计算间距指标（Spacing）
        
        参数:
            pareto_front: 帕累托前沿点集
            
        返回:
            间距值
        """
        if len(pareto_front) < 2:
            return 0.0
            
        pf_array = np.array(pareto_front)
        distances = []
        
        for i in range(len(pf_array)):
            min_dist = float('inf')
            for j in range(len(pf_array)):
                if i != j:
                    dist = np.sqrt(np.sum((pf_array[i] - pf_array[j])**2))
                    min_dist = min(min_dist, dist)
            distances.append(min_dist)
            
        mean_dist = np.mean(distances)
        spacing = np.sqrt(np.mean([(d - mean_dist)**2 for d in distances]))
        
        return spacing
    
    @staticmethod
    def calculate_igd(pareto_front: List[List[float]], reference_front: List[List[float]]) -> float:
        """
        计算IGD指标（Inverted Generational Distance）
        
        参数:
            pareto_front: 算法得到的帕累托前沿
            reference_front: 参考帕累托前沿
            
        返回:
            IGD值
        """
        if not pareto_front or not reference_front:
            return float('inf')
            
        pf_array = np.array(pareto_front)
        ref_array = np.array(reference_front)
        
        distances = []
        for ref_point in ref_array:
            min_dist = float('inf')
            for pf_point in pf_array:
                dist = np.sqrt(np.sum((ref_point - pf_point)**2))
                min_dist = min(min_dist, dist)
            distances.append(min_dist)
            
        return np.mean(distances)
    
    @staticmethod
    def calculate_diversity_index(pareto_front: List[List[float]]) -> float:
        """
        计算多样性指数
        
        参数:
            pareto_front: 帕累托前沿点集
            
        返回:
            多样性指数
        """
        if len(pareto_front) < 2:
            return 0.0
            
        pf_array = np.array(pareto_front)
        
        # 计算目标空间的标准差
        std_values = np.std(pf_array, axis=0)
        diversity = np.mean(std_values)
        
        return diversity


class ResourceMonitor:
    """资源使用监控类"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.peak_memory = 0
        self.monitoring = False
        self.memory_samples = []
        
    def start_monitoring(self):
        """开始监控"""
        self.start_time = time.time()
        self.peak_memory = 0
        self.monitoring = True
        self.memory_samples = []
        
        # 启动内存监控线程
        self.monitor_thread = threading.Thread(target=self._monitor_memory)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
        # 启动内存跟踪
        tracemalloc.start()
        
    def stop_monitoring(self) -> Dict[str, Any]:
        """停止监控并返回结果"""
        self.end_time = time.time()
        self.monitoring = False
        
        # 获取内存使用统计
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        
        return {
            'execution_time': self.end_time - self.start_time,
            'peak_memory_mb': peak / 1024 / 1024,
            'current_memory_mb': current / 1024 / 1024,
            'system_peak_memory_mb': self.peak_memory,
            'memory_samples': self.memory_samples
        }
        
    def _monitor_memory(self):
        """监控内存使用的后台线程"""
        process = psutil.Process()
        
        while self.monitoring:
            try:
                memory_info = process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024
                self.peak_memory = max(self.peak_memory, memory_mb)
                self.memory_samples.append(memory_mb)
                time.sleep(0.1)  # 每100ms采样一次
            except:
                break


class PerformanceComparator:
    """性能对比测试主类"""
    
    def __init__(self, test_config: Dict[str, Any] = None):
        """
        初始化性能对比器
        
        参数:
            test_config: 测试配置
        """
        self.test_config = test_config or self._get_default_test_config()
        self.metrics = PerformanceMetrics()
        self.results = {}
        
    def _get_default_test_config(self) -> Dict[str, Any]:
        """获取默认测试配置"""
        return {
            'test_runs': 5,  # 测试运行次数
            'max_aircraft': 8,  # 最大飞机数量（用于快速测试）
            'output_folder': 'test_results/performance_comparison/',
            'algorithms': {
                'standard_nsga2': {'use_akid': False},
                'akid_default': {'use_akid': True, 'config': 'default'},
                'akid_conservative': {'use_akid': True, 'config': 'conservative'},
                'akid_aggressive': {'use_akid': True, 'config': 'aggressive'}
            }
        }
    
    def load_test_data(self):
        """加载测试数据"""
        print("正在加载性能测试数据...")
        
        # 读取机场布局数据
        with open('doh1.txt', 'r') as file:
            lines = file.readlines()

        # 解析数据部分标记
        node_section_start = lines.index('%SECTION%1%;Nodes;\n') + 1
        edge_section_start = lines.index('%SECTION%2%;Edges;\n') + 1
        aircraft_section_start = lines.index('%SECTION%3%;Aircraft;\n') + 1

        # 解析节点数据
        node_data_lines = lines[node_section_start + 1:edge_section_start - 1]
        node_data = []
        for line in node_data_lines:
            if line.strip() and not line.startswith('%'):
                parts = line.strip().split(';')
                if len(parts) >= 4:
                    node_id, x, y, specification = parts[1:5]
                    node_data.append([node_id, float(x), float(y), specification])

        nodes_df = pd.DataFrame(node_data, columns=['Node ID', 'X', 'Y', 'Specification'])

        # 解析边数据
        edge_data_lines = lines[edge_section_start + 1:aircraft_section_start - 1]
        edge_data = []
        for line in edge_data_lines:
            if line.strip() and not line.startswith('%'):
                parts = line.strip().split(';')
                if len(parts) >= 6:
                    edge_id, start_node, end_node, directed, length, edge_type = parts[1:7]
                    edge_data.append([edge_id, start_node, end_node, directed, float(length), edge_type, []])

        edges_df = pd.DataFrame(edge_data, columns=['Edge ID', 'Start Node', 'End Node', 'Directed', 'Length', 'Type', 'Unavailable Time Windows'])

        # 解析飞机数据（限制数量用于测试）
        aircraft_data_lines = lines[aircraft_section_start + 1:]
        aircraft_data = []
        count = 0
        max_aircraft = self.test_config['max_aircraft']
        
        for line in aircraft_data_lines:
            if line.strip() and not line.startswith('%') and count < max_aircraft:
                parts = line.strip().split(';')
                if len(parts) >= 16:
                    aircraft_type = parts[1].strip().strip("'")
                    start_node = parts[2].strip().strip("'")
                    end_node = parts[3].strip().strip("'")
                    start_time_str = parts[4].strip("[]")
                    start_time_values = start_time_str.split(';')
                    start_time = float(start_time_values[0].strip())
                    weight_class = parts[16].strip().strip("'")
                    aircraft_data.append([aircraft_type, start_node, end_node, start_time, weight_class])
                    count += 1

        aircraft_df = pd.DataFrame(aircraft_data, columns=['Type', 'Start Node', 'End Node', 'Start Time', 'Weight Class'])
        aircraft_df = aircraft_df.sort_values(by='Start Time').reset_index(drop=True)
        aircraft_df['Aircraft ID'] = aircraft_df.index

        # 速度配置数据
        speed_profile_df = pd.read_csv('doh1_database.csv')

        # 创建机场图
        airport_graph = nx.Graph()
        for _, edge in edges_df.iterrows():
            start_node = edge['Start Node']
            end_node = edge['End Node']
            length = edge['Length']
            time_window = edge['Unavailable Time Windows']
            airport_graph.add_edge(start_node, end_node, length=length, unavailable_time_windows=time_window)
            airport_graph.add_edge(end_node, start_node, length=length, unavailable_time_windows=time_window)

        print(f"测试数据加载完成: {len(nodes_df)}个节点, {len(edges_df)}条边, {len(aircraft_df)}架飞机")
        
        return aircraft_df, airport_graph, nodes_df, edges_df, speed_profile_df
    
    def run_algorithm_test(self, algorithm_name: str, algorithm_config: Dict[str, Any], 
                          test_data: Tuple) -> Dict[str, Any]:
        """
        运行单个算法测试
        
        参数:
            algorithm_name: 算法名称
            algorithm_config: 算法配置
            test_data: 测试数据
            
        返回:
            测试结果
        """
        aircraft_df, airport_graph, nodes_df, edges_df, speed_profile_df = test_data
        
        print(f"  正在测试 {algorithm_name}...")
        
        # 创建输出文件夹
        output_folder = os.path.join(self.test_config['output_folder'], algorithm_name)
        os.makedirs(output_folder, exist_ok=True)
        
        # 资源监控器
        monitor = ResourceMonitor()
        
        # 存储多次运行的结果
        run_results = []
        
        for run_idx in range(self.test_config['test_runs']):
            print(f"    运行 {run_idx + 1}/{self.test_config['test_runs']}...")
            
            try:
                # 开始监控
                monitor.start_monitoring()
                
                # 根据算法类型选择运行函数
                if algorithm_config['use_akid']:
                    akid_config = get_akid_config(algorithm_config['config'])
                    result = MARMT_RK_global_with_akid(
                        aircraft_df, airport_graph, nodes_df, edges_df, speed_profile_df,
                        run_idx + 1, output_folder,
                        use_akid=True, akid_config=akid_config,
                        use_warmup_initialization=False  # 测试时不使用预热
                    )
                    
                    if len(result) == 3:
                        pareto_front, pareto_set, akid_report = result
                    else:
                        pareto_front, pareto_set = result
                        akid_report = None
                else:
                    pareto_front, pareto_set = MARMT_RK_global(
                        aircraft_df, airport_graph, nodes_df, edges_df, speed_profile_df,
                        run_idx + 1, output_folder,
                        use_warmup_initialization=False  # 测试时不使用预热
                    )
                    akid_report = None
                
                # 停止监控
                resource_usage = monitor.stop_monitoring()
                
                # 计算性能指标
                if pareto_front:
                    # 只取前两个目标（时间和燃油）
                    pf_objectives = [[pf[0], pf[1]] for pf in pareto_front]
                    
                    metrics = {
                        'pareto_front_size': len(pareto_front),
                        'hypervolume': self.metrics.calculate_hypervolume(pf_objectives),
                        'spacing': self.metrics.calculate_spacing(pf_objectives),
                        'diversity': self.metrics.calculate_diversity_index(pf_objectives),
                        'best_time': min(pf[0] for pf in pareto_front),
                        'best_fuel': min(pf[1] for pf in pareto_front),
                        'avg_time': np.mean([pf[0] for pf in pareto_front]),
                        'avg_fuel': np.mean([pf[1] for pf in pareto_front])
                    }
                else:
                    metrics = {
                        'pareto_front_size': 0,
                        'hypervolume': 0.0,
                        'spacing': 0.0,
                        'diversity': 0.0,
                        'best_time': float('inf'),
                        'best_fuel': float('inf'),
                        'avg_time': float('inf'),
                        'avg_fuel': float('inf')
                    }
                
                # 添加资源使用信息
                metrics.update(resource_usage)
                
                # 添加AKID特定信息
                if akid_report:
                    metrics['akid_statistics'] = {
                        'knowledge_injections': akid_report.get('knowledge_injection', {}).get('total_injections', 0),
                        'average_diversity': akid_report.get('diversity_monitoring', {}).get('average_diversity', 0),
                        'knowledge_base_size': akid_report.get('knowledge_base', {}).get('final_size', 0)
                    }
                
                run_results.append(metrics)
                
            except Exception as e:
                print(f"    运行 {run_idx + 1} 失败: {str(e)}")
                error_metrics = {
                    'error': str(e),
                    'pareto_front_size': 0,
                    'execution_time': 0,
                    'peak_memory_mb': 0
                }
                run_results.append(error_metrics)
        
        # 计算统计摘要
        successful_runs = [r for r in run_results if 'error' not in r]
        
        if successful_runs:
            summary = {
                'successful_runs': len(successful_runs),
                'total_runs': len(run_results),
                'success_rate': len(successful_runs) / len(run_results),
                'avg_pareto_front_size': np.mean([r['pareto_front_size'] for r in successful_runs]),
                'avg_hypervolume': np.mean([r['hypervolume'] for r in successful_runs]),
                'avg_spacing': np.mean([r['spacing'] for r in successful_runs]),
                'avg_diversity': np.mean([r['diversity'] for r in successful_runs]),
                'avg_execution_time': np.mean([r['execution_time'] for r in successful_runs]),
                'avg_peak_memory': np.mean([r['peak_memory_mb'] for r in successful_runs]),
                'best_time_overall': min([r['best_time'] for r in successful_runs]),
                'best_fuel_overall': min([r['best_fuel'] for r in successful_runs]),
                'std_hypervolume': np.std([r['hypervolume'] for r in successful_runs]),
                'std_execution_time': np.std([r['execution_time'] for r in successful_runs])
            }
            
            # AKID特定统计
            akid_runs = [r for r in successful_runs if 'akid_statistics' in r]
            if akid_runs:
                summary['avg_knowledge_injections'] = np.mean([r['akid_statistics']['knowledge_injections'] for r in akid_runs])
                summary['avg_akid_diversity'] = np.mean([r['akid_statistics']['average_diversity'] for r in akid_runs])
                summary['avg_knowledge_base_size'] = np.mean([r['akid_statistics']['knowledge_base_size'] for r in akid_runs])
        else:
            summary = {
                'successful_runs': 0,
                'total_runs': len(run_results),
                'success_rate': 0.0,
                'errors': [r.get('error', 'Unknown error') for r in run_results]
            }
        
        return {
            'algorithm_name': algorithm_name,
            'summary': summary,
            'detailed_results': run_results
        }
    
    def run_performance_comparison(self):
        """运行完整的性能对比测试"""
        print("="*80)
        print("AKID-NSGA2 性能对比测试")
        print("="*80)
        print("按照实施计划第6.2节要求执行性能对比测试...")
        print(f"测试配置: {self.test_config['test_runs']}次运行, 最大{self.test_config['max_aircraft']}架飞机")
        print()
        
        # 加载测试数据
        test_data = self.load_test_data()
        
        # 运行各算法测试
        for algorithm_name, algorithm_config in self.test_config['algorithms'].items():
            result = self.run_algorithm_test(algorithm_name, algorithm_config, test_data)
            self.results[algorithm_name] = result
        
        # 生成对比报告
        self._generate_comparison_report()
        
        # 生成可视化图表
        self._generate_visualization()
        
        print("\n" + "="*80)
        print("性能对比测试完成！")
        print("="*80)
        
        return self.results
    
    def _generate_comparison_report(self):
        """生成对比报告"""
        print("\n正在生成性能对比报告...")
        
        # 创建输出文件夹
        os.makedirs(self.test_config['output_folder'], exist_ok=True)
        
        # 生成详细报告
        detailed_report = {
            'test_configuration': self.test_config,
            'test_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'algorithm_results': self.results
        }
        
        # 保存详细报告
        detailed_file = os.path.join(self.test_config['output_folder'], 'detailed_performance_report.json')
        with open(detailed_file, 'w', encoding='utf-8') as f:
            json.dump(detailed_report, f, indent=2, ensure_ascii=False)
        
        # 生成摘要报告
        summary_report = self._create_summary_report()
        
        summary_file = os.path.join(self.test_config['output_folder'], 'performance_summary.json')
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_report, f, indent=2, ensure_ascii=False)
        
        # 生成文本报告
        text_report = self._create_text_report()
        
        text_file = os.path.join(self.test_config['output_folder'], 'performance_report.txt')
        with open(text_file, 'w', encoding='utf-8') as f:
            f.write(text_report)
        
        print(f"  详细报告: {detailed_file}")
        print(f"  摘要报告: {summary_file}")
        print(f"  文本报告: {text_file}")
    
    def _create_summary_report(self) -> Dict[str, Any]:
        """创建摘要报告"""
        summary = {
            'test_overview': {
                'algorithms_tested': len(self.results),
                'runs_per_algorithm': self.test_config['test_runs'],
                'aircraft_count': self.test_config['max_aircraft']
            },
            'performance_comparison': {},
            'rankings': {}
        }
        
        # 收集成功运行的结果
        successful_algorithms = {}
        for alg_name, result in self.results.items():
            if result['summary']['success_rate'] > 0:
                successful_algorithms[alg_name] = result['summary']
        
        if successful_algorithms:
            # 性能对比
            metrics = ['avg_hypervolume', 'avg_spacing', 'avg_diversity', 'avg_execution_time', 'avg_peak_memory']
            
            for metric in metrics:
                summary['performance_comparison'][metric] = {}
                for alg_name, alg_summary in successful_algorithms.items():
                    if metric in alg_summary:
                        summary['performance_comparison'][metric][alg_name] = alg_summary[metric]
            
            # 排名分析
            if len(successful_algorithms) > 1:
                # 超体积排名（越大越好）
                hv_ranking = sorted(successful_algorithms.items(), 
                                  key=lambda x: x[1].get('avg_hypervolume', 0), reverse=True)
                summary['rankings']['hypervolume'] = [alg[0] for alg in hv_ranking]
                
                # 执行时间排名（越小越好）
                time_ranking = sorted(successful_algorithms.items(), 
                                    key=lambda x: x[1].get('avg_execution_time', float('inf')))
                summary['rankings']['execution_time'] = [alg[0] for alg in time_ranking]
                
                # 内存使用排名（越小越好）
                memory_ranking = sorted(successful_algorithms.items(), 
                                      key=lambda x: x[1].get('avg_peak_memory', float('inf')))
                summary['rankings']['memory_usage'] = [alg[0] for alg in memory_ranking]
        
        return summary
    
    def _create_text_report(self) -> str:
        """创建文本格式报告"""
        lines = []
        lines.append("AKID-NSGA2 性能对比测试报告")
        lines.append("=" * 80)
        lines.append(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append(f"测试配置: {self.test_config['test_runs']}次运行, {self.test_config['max_aircraft']}架飞机")
        lines.append("")
        
        # 算法结果摘要
        lines.append("算法性能摘要:")
        lines.append("-" * 40)
        
        for alg_name, result in self.results.items():
            summary = result['summary']
            lines.append(f"\n{alg_name}:")
            lines.append(f"  成功率: {summary.get('success_rate', 0):.2%}")
            
            if summary.get('success_rate', 0) > 0:
                lines.append(f"  平均帕累托前沿大小: {summary.get('avg_pareto_front_size', 0):.1f}")
                lines.append(f"  平均超体积: {summary.get('avg_hypervolume', 0):.4f}")
                lines.append(f"  平均执行时间: {summary.get('avg_execution_time', 0):.2f}秒")
                lines.append(f"  平均内存峰值: {summary.get('avg_peak_memory', 0):.1f}MB")
                
                if 'avg_knowledge_injections' in summary:
                    lines.append(f"  平均知识注入次数: {summary['avg_knowledge_injections']:.1f}")
            else:
                lines.append("  所有运行均失败")
        
        # 性能对比分析
        successful_results = {k: v for k, v in self.results.items() 
                            if v['summary'].get('success_rate', 0) > 0}
        
        if len(successful_results) > 1:
            lines.append("\n\n性能对比分析:")
            lines.append("-" * 40)
            
            # 寻找最佳算法
            best_hv = max(successful_results.items(), 
                         key=lambda x: x[1]['summary'].get('avg_hypervolume', 0))
            lines.append(f"最佳超体积: {best_hv[0]} ({best_hv[1]['summary']['avg_hypervolume']:.4f})")
            
            best_time = min(successful_results.items(), 
                           key=lambda x: x[1]['summary'].get('avg_execution_time', float('inf')))
            lines.append(f"最快执行: {best_time[0]} ({best_time[1]['summary']['avg_execution_time']:.2f}秒)")
            
            best_memory = min(successful_results.items(), 
                             key=lambda x: x[1]['summary'].get('avg_peak_memory', float('inf')))
            lines.append(f"最低内存: {best_memory[0]} ({best_memory[1]['summary']['avg_peak_memory']:.1f}MB)")
        
        # AKID特定分析
        akid_results = {k: v for k, v in successful_results.items() 
                       if 'akid' in k.lower()}
        
        if akid_results:
            lines.append("\n\nAKID算法分析:")
            lines.append("-" * 40)
            
            for alg_name, result in akid_results.items():
                summary = result['summary']
                lines.append(f"\n{alg_name}:")
                if 'avg_knowledge_injections' in summary:
                    lines.append(f"  平均知识注入次数: {summary['avg_knowledge_injections']:.1f}")
                    lines.append(f"  平均知识库大小: {summary.get('avg_knowledge_base_size', 0):.1f}")
                    lines.append(f"  AKID多样性指标: {summary.get('avg_akid_diversity', 0):.4f}")
        
        return "\n".join(lines)
    
    def _generate_visualization(self):
        """生成可视化图表"""
        print("正在生成性能对比图表...")
        
        # 收集成功的结果
        successful_results = {k: v for k, v in self.results.items() 
                            if v['summary'].get('success_rate', 0) > 0}
        
        if len(successful_results) < 2:
            print("  可视化需要至少2个成功的算法结果")
            return
        
        # 创建图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('AKID-NSGA2 性能对比分析', fontsize=16)
        
        alg_names = list(successful_results.keys())
        
        # 1. 超体积对比
        hypervolumes = [successful_results[alg]['summary'].get('avg_hypervolume', 0) for alg in alg_names]
        hv_stds = [successful_results[alg]['summary'].get('std_hypervolume', 0) for alg in alg_names]
        
        ax1.bar(alg_names, hypervolumes, yerr=hv_stds, capsize=5)
        ax1.set_title('平均超体积对比')
        ax1.set_ylabel('超体积值')
        ax1.tick_params(axis='x', rotation=45)
        
        # 2. 执行时间对比
        exec_times = [successful_results[alg]['summary'].get('avg_execution_time', 0) for alg in alg_names]
        time_stds = [successful_results[alg]['summary'].get('std_execution_time', 0) for alg in alg_names]
        
        ax2.bar(alg_names, exec_times, yerr=time_stds, capsize=5, color='orange')
        ax2.set_title('平均执行时间对比')
        ax2.set_ylabel('执行时间 (秒)')
        ax2.tick_params(axis='x', rotation=45)
        
        # 3. 内存使用对比
        memory_usage = [successful_results[alg]['summary'].get('avg_peak_memory', 0) for alg in alg_names]
        
        ax3.bar(alg_names, memory_usage, color='green')
        ax3.set_title('平均内存峰值对比')
        ax3.set_ylabel('内存使用 (MB)')
        ax3.tick_params(axis='x', rotation=45)
        
        # 4. 帕累托前沿大小对比
        pf_sizes = [successful_results[alg]['summary'].get('avg_pareto_front_size', 0) for alg in alg_names]
        
        ax4.bar(alg_names, pf_sizes, color='red')
        ax4.set_title('平均帕累托前沿大小对比')
        ax4.set_ylabel('帕累托前沿大小')
        ax4.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        # 保存图表
        chart_file = os.path.join(self.test_config['output_folder'], 'performance_comparison_charts.png')
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"  性能对比图表: {chart_file}")


def run_performance_comparison_test():
    """运行性能对比测试的主函数"""
    
    # 检查必要文件
    required_files = ['doh1.txt', 'doh1_database.csv']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ 缺失必要文件: {missing_files}")
        return False
    
    # 创建性能对比器
    comparator = PerformanceComparator()
    
    try:
        # 运行对比测试
        results = comparator.run_performance_comparison()
        
        # 输出测试总结
        print("\n测试总结:")
        successful_algs = [alg for alg, result in results.items() 
                          if result['summary'].get('success_rate', 0) > 0]
        failed_algs = [alg for alg, result in results.items() 
                      if result['summary'].get('success_rate', 0) == 0]
        
        print(f"  成功算法: {len(successful_algs)}")
        print(f"  失败算法: {len(failed_algs)}")
        
        if successful_algs:
            print(f"  成功算法列表: {', '.join(successful_algs)}")
        
        if failed_algs:
            print(f"  失败算法列表: {', '.join(failed_algs)}")
        
        # 输出最佳表现
        if len(successful_algs) > 1:
            best_hv = max(successful_algs, 
                         key=lambda x: results[x]['summary'].get('avg_hypervolume', 0))
            best_time = min(successful_algs, 
                           key=lambda x: results[x]['summary'].get('avg_execution_time', float('inf')))
            
            print(f"\n性能表现:")
            print(f"  最佳质量(超体积): {best_hv}")
            print(f"  最佳速度(执行时间): {best_time}")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能对比测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("="*80)
    print("AKID-NSGA2 性能对比测试")
    print("="*80)
    print("按照实施计划第6.2节要求执行性能对比测试...")
    print("测试项目:")
    print("1. 收敛性对比：AKID-NSGA2 vs 标准NSGA-II")
    print("2. 多样性对比：帕累托前沿分布质量")
    print("3. 计算效率对比：运行时间和内存使用")
    print("="*80)
    
    success = run_performance_comparison_test()
    
    if success:
        print("\n✅ 性能对比测试完成！")
        print("详细结果请查看: test_results/performance_comparison/")
    else:
        print("\n❌ 性能对比测试失败！")
        print("请检查错误信息并重新运行。") 