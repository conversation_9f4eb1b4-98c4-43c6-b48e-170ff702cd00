"""
环境选择模块 - 实现NSGA-II算法的环境选择操作

本模块使用DEAP库实现NSGA-II算法的环境选择操作，包括:
1. 合并父代和子代种群
2. 基于非支配排序和拥挤度距离进行选择
3. 提取帕累托前沿和帕累托集
"""

from deap import base, creator, tools
import numpy as np


def constraint_aware_nsga2_selection(individuals, k):
    """
    约束感知的NSGA-II选择算法

    优先选择可行解，对于不可行解按约束违反程度排序

    参数:
        individuals: 个体列表
        k: 选择的个体数量

    返回:
        选择后的个体列表
    """
    # 分离可行解和不可行解
    feasible = [ind for ind in individuals if ind.constraints == 0]
    infeasible = [ind for ind in individuals if ind.constraints > 0]

    selected = []

    # 优先从可行解中选择
    if feasible:
        if len(feasible) >= k:
            # 可行解足够，直接使用NSGA-II选择
            selected = tools.selNSGA2(feasible, k)
        else:
            # 可行解不足，全部选择
            selected.extend(feasible)
            remaining = k - len(feasible)

            # 从不可行解中选择剩余个体
            if infeasible and remaining > 0:
                # 按约束违反程度排序
                infeasible.sort(key=lambda x: x.constraints)
                selected.extend(infeasible[:remaining])
    else:
        # 没有可行解，按约束违反程度选择
        if infeasible:
            infeasible.sort(key=lambda x: x.constraints)
            selected = infeasible[:k]

    return selected


# 创建DEAP适应度和个体类型（避免重复创建）
try:
    creator.create("FitnessMulti", base.Fitness, weights=(-1.0, -1.0))
except RuntimeError:
    pass  # 如果已经创建，则跳过

try:
    creator.create("Individual", list, fitness=creator.FitnessMulti)
except RuntimeError:
    pass  # 如果已经创建，则跳过


def remove_duplicate_solutions(individuals, tolerance=1e-6):
    """
    移除重复的解
    
    基于目标函数值移除重复解，保留第一个遇到的解
    
    参数:
        individuals: DEAP个体列表
        tolerance: 容忍度，用于判断目标函数值是否相同
        
    返回:
        去重后的个体列表
    """
    unique_individuals = []
    seen_objectives = []
    
    for individual in individuals:
        obj = individual.fitness.values
        is_duplicate = False
        
        for seen_obj in seen_objectives:
            if (abs(obj[0] - seen_obj[0]) < tolerance and 
                abs(obj[1] - seen_obj[1]) < tolerance):
                is_duplicate = True
                break
                
        if not is_duplicate:
            unique_individuals.append(individual)
            seen_objectives.append(obj)
            
    return unique_individuals


def environmental_selection(parent_population, offspring_population, parent_fitness, offspring_fitness, population_size):
    """
    执行NSGA-II环境选择操作

    将父代和子代合并，然后基于非支配排序和拥挤度距离选择下一代种群。
    同时提取当前种群的帕累托前沿和帕累托集。

    参数:
        parent_population: 父代种群
        offspring_population: 子代种群
        parent_fitness: 父代适应度值
        offspring_fitness: 子代适应度值
        population_size: 种群大小

    返回:
        new_population: 选择后的新种群
        new_fitness: 新种群的适应度值
        pareto_front: 帕累托前沿（目标空间）
        pareto_set: 帕累托集（决策空间）
    """
    # 合并父代和子代
    combined_population = parent_population + offspring_population
    combined_fitness = parent_fitness + offspring_fitness

    # 转换为DEAP兼容的个体
    deap_individuals = []

    for individual_data, fitness_values in zip(combined_population, combined_fitness):
        # 创建DEAP个体（使用列表表示）
        deap_individual = creator.Individual(list(individual_data.values()))

        # 存储原始字典数据，以便后续还原
        deap_individual.original_data = individual_data

        # 设置适应度值（前两个值是目标函数值，第三个是约束违反值）
        deap_individual.fitness.values = fitness_values[:2]
        deap_individual.constraints = fitness_values[2]

        deap_individuals.append(deap_individual)

    # 在选择前移除重复解
    deap_individuals = remove_duplicate_solutions(deap_individuals, tolerance=1e-6)

    # 修复：执行约束感知的NSGA-II选择
    selected_individuals = constraint_aware_nsga2_selection(deap_individuals, population_size)

    # 提取选择后的种群
    new_population = [individual.original_data for individual in selected_individuals]
    new_fitness = [(individual.fitness.values[0], individual.fitness.values[1], individual.constraints)
                  for individual in selected_individuals]

    # 修复：从选择后的种群中识别帕累托前沿
    pareto_individuals = tools.sortNondominated(selected_individuals, len(selected_individuals), first_front_only=True)[0]
    
    # 再次去重帕累托前沿中的解
    pareto_individuals = remove_duplicate_solutions(pareto_individuals, tolerance=1e-6)

    # 提取帕累托前沿和帕累托集
    pareto_front = [(individual.fitness.values[0], individual.fitness.values[1], individual.constraints)
                    for individual in pareto_individuals]
    pareto_set = [individual.original_data for individual in pareto_individuals]

    return new_population, new_fitness, pareto_front, pareto_set


def environmental_selection_with_knowledge_extraction(parent_population, offspring_population, 
                                                    parent_fitness, offspring_fitness, 
                                                    population_size, knowledge_base=None):
    """
    增强的环境选择，支持知识提取
    
    在原有NSGA-II环境选择基础上，增加了知识库更新功能。
    将帕累托前沿中的优秀解自动添加到知识库中。
    
    参数:
        parent_population: 父代种群
        offspring_population: 子代种群
        parent_fitness: 父代适应度值
        offspring_fitness: 子代适应度值
        population_size: 种群大小
        knowledge_base: 动态知识库实例（可选）
        
    返回:
        new_population: 选择后的新种群
        new_fitness: 新种群的适应度值
        pareto_front: 帕累托前沿（目标空间）
        pareto_set: 帕累托集（决策空间）
    """
    # 执行原有NSGA-II选择
    new_population, new_fitness, pareto_front, pareto_set = environmental_selection(
        parent_population, offspring_population, parent_fitness, offspring_fitness, population_size)
    
    # 如果启用知识库，提取精英解
    if knowledge_base is not None:
        # 将帕累托前沿中的可行解添加到知识库
        for ind, fit in zip(pareto_set, pareto_front):
            if fit[2] == 0:  # 仅添加可行解（约束违反值为0）
                # 添加解到知识库，如果重复则不会添加
                knowledge_base.add_solution(ind, fit[:2], None)
                
        # 也可以从整个新种群中选择一些高质量解
        for ind, fit in zip(new_population, new_fitness):
            if fit[2] == 0:  # 仅添加可行解
                # 可以设置一定的质量阈值，只添加优质解
                # 这里简化处理，让知识库自己判断是否添加
                knowledge_base.add_solution(ind, fit[:2], None)
    
    return new_population, new_fitness, pareto_front, pareto_set


def adaptive_environmental_selection(parent_population, offspring_population, 
                                   parent_fitness, offspring_fitness, 
                                   population_size, diversity_monitor=None, 
                                   knowledge_base=None, generation=None):
    """
    自适应环境选择
    
    根据多样性监控器的反馈，动态调整选择策略，并支持知识提取。
    
    参数:
        parent_population: 父代种群
        offspring_population: 子代种群
        parent_fitness: 父代适应度值
        offspring_fitness: 子代适应度值
        population_size: 种群大小
        diversity_monitor: 多样性监控器实例（可选）
        knowledge_base: 动态知识库实例（可选）
        generation: 当前代数（可选）
        
    返回:
        new_population: 选择后的新种群
        new_fitness: 新种群的适应度值
        pareto_front: 帕累托前沿（目标空间）
        pareto_set: 帕累托集（决策空间）
        selection_stats: 选择统计信息
    """
    # 执行基础环境选择
    new_population, new_fitness, pareto_front, pareto_set = environmental_selection_with_knowledge_extraction(
        parent_population, offspring_population, parent_fitness, offspring_fitness, 
        population_size, knowledge_base)
    
    selection_stats = {
        'pareto_front_size': len(pareto_front),
        'feasible_solutions': sum(1 for fit in new_fitness if fit[2] == 0),
        'selection_method': 'standard_nsga2'
    }
    
    # 如果有多样性监控器，进行多样性分析
    if diversity_monitor is not None:
        current_objectives = [fit[:2] for fit in new_fitness]
        current_diversity = diversity_monitor._calculate_diversity(np.array(current_objectives))
        
        selection_stats['current_diversity'] = current_diversity
        selection_stats['diversity_trend'] = 'unknown'
        
        # 如果多样性过低，可以考虑调整选择策略
        if current_diversity < diversity_monitor.diversity_threshold * 0.5:
            # 多样性过低时，可以采用更倾向于多样性的选择策略
            selection_stats['selection_method'] = 'diversity_enhanced'
            selection_stats['diversity_enhancement'] = True
            
            # 这里可以实现多样性增强的选择逻辑
            # 例如：增加拥挤度距离的权重，或使用其他多样性保持机制
            new_population, new_fitness = _diversity_enhanced_selection(
                new_population, new_fitness, population_size)
        else:
            selection_stats['diversity_enhancement'] = False
    
    return new_population, new_fitness, pareto_front, pareto_set, selection_stats


def _diversity_enhanced_selection(population, fitness_values, population_size):
    """
    多样性增强选择
    
    当检测到种群多样性过低时，采用更倾向于保持多样性的选择策略。
    
    参数:
        population: 当前种群
        fitness_values: 适应度值
        population_size: 目标种群大小
        
    返回:
        enhanced_population: 多样性增强后的种群
        enhanced_fitness: 对应的适应度值
    """
    if len(population) <= population_size:
        return population, fitness_values
    
    # 提取目标函数值
    objectives = np.array([fit[:2] for fit in fitness_values])
    
    # 计算拥挤度距离
    n_solutions = len(population)
    crowding_distances = np.zeros(n_solutions)
    
    for obj_idx in range(objectives.shape[1]):
        # 按当前目标排序
        sorted_indices = np.argsort(objectives[:, obj_idx])
        
        # 边界点设置为无穷大
        crowding_distances[sorted_indices[0]] = float('inf')
        crowding_distances[sorted_indices[-1]] = float('inf')
        
        # 计算目标函数范围
        obj_range = objectives[sorted_indices[-1], obj_idx] - objectives[sorted_indices[0], obj_idx]
        
        if obj_range > 0:
            # 计算中间点的拥挤度距离
            for i in range(1, n_solutions - 1):
                distance = (objectives[sorted_indices[i + 1], obj_idx] - 
                          objectives[sorted_indices[i - 1], obj_idx]) / obj_range
                crowding_distances[sorted_indices[i]] += distance
    
    # 基于拥挤度距离选择多样性好的解
    # 优先选择可行解，然后按拥挤度距离排序
    feasible_indices = [i for i, fit in enumerate(fitness_values) if fit[2] == 0]
    infeasible_indices = [i for i, fit in enumerate(fitness_values) if fit[2] > 0]
    
    selected_indices = []
    
    # 先从可行解中选择
    if feasible_indices:
        feasible_crowding = [(i, crowding_distances[i]) for i in feasible_indices]
        feasible_crowding.sort(key=lambda x: x[1], reverse=True)  # 按拥挤度距离降序排列
        
        selected_count = min(len(feasible_crowding), population_size)
        selected_indices.extend([idx for idx, _ in feasible_crowding[:selected_count]])
    
    # 如果还需要更多个体，从不可行解中选择
    if len(selected_indices) < population_size and infeasible_indices:
        remaining_count = population_size - len(selected_indices)
        infeasible_crowding = [(i, crowding_distances[i]) for i in infeasible_indices]
        infeasible_crowding.sort(key=lambda x: x[1], reverse=True)
        
        selected_indices.extend([idx for idx, _ in infeasible_crowding[:remaining_count]])
    
    # 构建增强后的种群
    enhanced_population = [population[i] for i in selected_indices]
    enhanced_fitness = [fitness_values[i] for i in selected_indices]
    
    return enhanced_population, enhanced_fitness


def extract_elite_solutions_from_selection(population, fitness_values, 
                                         elite_ratio=0.2, quality_threshold=None):
    """
    从选择结果中提取精英解
    
    用于知识库更新或其他用途的精英解提取。
    
    参数:
        population: 种群
        fitness_values: 适应度值
        elite_ratio: 精英解比例
        quality_threshold: 质量阈值（可选）
        
    返回:
        elite_population: 精英解种群
        elite_fitness: 精英解适应度
    """
    # 只考虑可行解
    feasible_solutions = [(pop, fit) for pop, fit in zip(population, fitness_values) if fit[2] == 0]
    
    if not feasible_solutions:
        return [], []
    
    # 按目标函数值排序（简单加权和）
    feasible_solutions.sort(key=lambda x: x[1][0] + x[1][1])  # 两个目标的简单加权和
    
    # 选择前elite_ratio比例的解
    elite_count = max(1, int(len(feasible_solutions) * elite_ratio))
    elite_solutions = feasible_solutions[:elite_count]
    
    # 如果有质量阈值，进一步筛选
    if quality_threshold is not None:
        elite_solutions = [sol for sol in elite_solutions 
                          if (sol[1][0] + sol[1][1]) <= quality_threshold]
    
    if elite_solutions:
        elite_population, elite_fitness = zip(*elite_solutions)
        return list(elite_population), list(elite_fitness)
    else:
        return [], []


def calculate_selection_diversity(fitness_values):
    """
    计算选择结果的多样性指标
    
    参数:
        fitness_values: 适应度值列表
        
    返回:
        diversity_metrics: 多样性指标字典
    """
    if len(fitness_values) <= 1:
        return {'diversity': 0.0, 'spread': 0.0, 'coverage': 0.0}
    
    # 提取目标函数值
    objectives = np.array([fit[:2] for fit in fitness_values])
    
    # 计算目标空间的多样性
    std_devs = np.std(objectives, axis=0)
    diversity = np.mean(std_devs)
    
    # 计算分布范围
    ranges = np.max(objectives, axis=0) - np.min(objectives, axis=0)
    spread = np.mean(ranges)
    
    # 计算覆盖面积（对于二维目标空间）
    if objectives.shape[0] >= 2:
        # 使用凸包计算覆盖面积
        try:
            from scipy.spatial import ConvexHull
            hull = ConvexHull(objectives)
            coverage = hull.volume  # 对于2D是面积
        except:
            # 如果scipy不可用，使用简单的矩形面积估算
            coverage = ranges[0] * ranges[1] if len(ranges) >= 2 else 0.0
    else:
        coverage = 0.0
    
    return {
        'diversity': diversity,
        'spread': spread, 
        'coverage': coverage,
        'objective_ranges': ranges.tolist()
    }
