"""
NSGA-II 核心算法模块

本包包含NSGA-II多目标优化算法的核心组件：
- 主算法流程：种群初始化、进化操作、环境选择
- 适应度评估：多目标函数计算和约束处理
- 遗传操作：交叉、变异操作
- 环境选择：非支配排序和拥挤距离计算
- 路径解码：将编码解转换为实际路径
"""

from .algorithm import (
    MARMT_RK_global, 
    MARMT_RK_global_with_akid,
    initialize_population,
    initialize_population_with_warmup,
    generate_knowledge_base_solutions
)
from .fitness import evaluate_population
from .operations import crossover_and_mutate
from .selection import environmental_selection
from .decoding import decode_individual

__version__ = "2.0.0"
__author__ = "NSGA-II Development Team"

__all__ = [
    'MARMT_RK_global',
    'MARMT_RK_global_with_akid', 
    'initialize_population',
    'initialize_population_with_warmup',
    'generate_knowledge_base_solutions',
    'evaluate_population',
    'crossover_and_mutate',
    'environmental_selection',
    'decode_individual'
] 