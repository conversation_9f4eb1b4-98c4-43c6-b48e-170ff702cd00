#!/usr/bin/env python3
"""
快速测试知识库初始化改进

验证改进后的知识库逻辑：
- 使用单目标优化预热时，知识库从单目标优化结果获取高质量解
- 种群保持全随机初始化，确保多样性
- 避免重复个体注入失败的问题
"""

from main_global import main
import os

def test_quick():
    """快速测试改进效果"""
    print("🚀 快速测试知识库初始化改进")
    print("="*60)
    
    print("\n这个测试将：")
    print("1. 运行改进后的AKID-NSGA2算法")
    print("2. 使用单目标优化预热初始化知识库")
    print("3. 种群采用全随机初始化")
    print("4. 观察知识注入效果")
    
    # 创建测试结果目录
    test_dir = 'results/quick_test_improvement/'
    os.makedirs(test_dir, exist_ok=True)
    
    print(f"\n结果将保存在: {test_dir}")
    print("\n开始测试...")
    
    # 模拟用户输入：选择快速演示模式
    import sys
    from io import StringIO
    
    # 备份原始stdin
    original_stdin = sys.stdin
    
    try:
        # 模拟用户输入"1"（快速演示模式）
        sys.stdin = StringIO("1\n")
        
        # 运行主程序
        main()
        
        print("\n✅ 测试完成！")
        print("\n请查看输出日志中的以下关键信息：")
        print("1. '使用单目标优化结果初始化知识库...' - 表示使用了改进的初始化")
        print("2. '知识库初始化完成：从单目标优化结果添加了 X 个高质量解'")
        print("3. '重新生成随机种群以确保多样性...'")
        print("4. '完成知识库与种群的分离式初始化'")
        print("5. 知识注入的成功率应该有所提高")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        
    finally:
        # 恢复原始stdin
        sys.stdin = original_stdin

if __name__ == "__main__":
    test_quick() 