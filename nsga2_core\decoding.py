"""
解码模块 - 将个体编码解码为滑行路径

本模块包含用于将NSGA-II算法中的个体编码解码为实际滑行路径的函数。
"""


def decode_individual(individual_encoding, aircraft_subgraph, node_vector, start_node, end_node):
    """
    将个体编码解码为滑行路径

    根据个体编码和子图，生成从起点到终点的滑行路径。
    路径生成基于贪心策略，每次选择编码值M1最大的未访问邻节点。

    参数:
        individual_encoding: 个体编码，包含每个节点的(M1,M2)值
        aircraft_subgraph: 飞机可用的子图
        node_vector: 节点向量，包含所有可能的节点
        start_node: 起始节点
        end_node: 终止节点

    返回:
        path_with_m2: 包含节点和M2值的路径列表，格式为[(node1, m2_1), (node2, m2_2), ...]
    """
    # 初始化路径和已访问节点集合
    path_with_m2 = []
    visited = {start_node}
    current_node = start_node

    try:
        # 使用NetworkX的邻接节点遍历
        while current_node != end_node:
            # 添加当前节点及其M2值到路径
            node_index = node_vector.index(current_node)
            m2_value = individual_encoding[node_index][1]
            path_with_m2.append((current_node, m2_value))

            # 获取当前节点的未访问邻节点
            neighbors = list(aircraft_subgraph.neighbors(current_node))
            unvisited_neighbors = [n for n in neighbors if n not in visited]

            # 如果没有未访问的邻接节点，结束循环
            if not unvisited_neighbors:
                break

            # 找到M1编码值最大的未访问邻节点作为下一个节点
            next_node = max(
                unvisited_neighbors,
                key=lambda x: individual_encoding[node_vector.index(x)][0]
            )

            # 标记为已访问并更新当前节点
            visited.add(next_node)
            current_node = next_node

        # 确保将终点节点添加到路径中
        if current_node == end_node:
            end_node_index = node_vector.index(end_node)
            end_node_m2 = individual_encoding[end_node_index][1]
            path_with_m2.append((end_node, end_node_m2))

    except Exception as e:
        # 处理可能的异常，如节点不在node_vector中
        print(f"解码过程中出现错误: {e}")
        # 返回一个只包含起点和终点的路径作为备选
        if not path_with_m2:
            # 如果路径为空，至少添加起点
            try:
                start_m2 = individual_encoding[node_vector.index(start_node)][1]
                path_with_m2.append((start_node, start_m2))
            except:
                path_with_m2.append((start_node, 0.5))  # 使用默认值

        # 如果路径中没有终点，添加终点
        if path_with_m2[-1][0] != end_node:
            try:
                end_m2 = individual_encoding[node_vector.index(end_node)][1]
                path_with_m2.append((end_node, end_m2))
            except:
                path_with_m2.append((end_node, 0.5))  # 使用默认值

    return path_with_m2
