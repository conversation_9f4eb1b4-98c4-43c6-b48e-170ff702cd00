"""
快速测试AKID核心模块功能
"""
import sys
sys.path.append('.')

from akid_modules.core import DynamicKnowledgeBase, DiversityMonitor
from akid_modules.injection import KnowledgeInjector
from akid_modules.segments import PathSegmentAnalyzer

def test_core_modules():
    """测试核心模块"""
    print('Testing DynamicKnowledgeBase...')
    dkb = DynamicKnowledgeBase()
    dkb.add_solution({'a': [1,2]}, (100.0, 50.0), 1)
    print(f'Size: {dkb.get_size()}')

    print('Testing DiversityMonitor...')
    dm = DiversityMonitor()
    diversity = dm.calculate_diversity([(100.0, 50.0), (110.0, 48.0)])
    print(f'Diversity: {diversity}')

    print('Testing KnowledgeInjector...')
    ki = KnowledgeInjector()
    print(f'Success rate: {ki.get_success_rate()}')

    print('Testing PathSegmentAnalyzer...')
    psa = PathSegmentAnalyzer()
    segments = psa._extract_segments([1, 2, 3, 4, 5], min_length=3)
    print(f'Segments: {segments}')

    print('All basic tests passed!')

if __name__ == "__main__":
    test_core_modules() 