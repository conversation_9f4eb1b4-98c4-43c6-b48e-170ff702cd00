"""
多目标机场滑行路径规划算法 (MARMT-RK) 全局版本

本模块实现了基于NSGA-II的多目标机场滑行路径规划算法，主要功能包括:
1. 构建每架飞机的可行滑行子图
2. 初始化种群（支持随机初始化和单目标优化预热初始化）
3. 执行进化算法迭代
4. 可视化和保存帕累托前沿

算法优化两个目标:
- 总滑行时间 (g1)
- 总燃油消耗 (g2)

新增功能:
- 单目标优化预热初始化策略：通过先分别优化单个目标，再组合最优解来初始化种群
"""

import networkx as nx
import random
import numpy as np
import os
import matplotlib.pyplot as plt
import copy
from nsga2_core.fitness import evaluate_population
from nsga2_core.operations import crossover_and_mutate
from nsga2_core.selection import environmental_selection


def plot_pareto_front(pareto_front, generation, output_folder, run_index):
    """
    绘制并保存帕累托前沿图

    参数:
        pareto_front: 帕累托前沿点集
        generation: 当前代数
        output_folder: 输出文件夹路径
        run_index: 运行索引
    """
    if not pareto_front:
        return  # 如果帕累托前沿为空，则跳过

    # 提取目标函数值
    taxiing_time_values = [point[0] for point in pareto_front]  # 第一个目标 (g1: 滑行时间)
    fuel_consumption_values = [point[1] for point in pareto_front]  # 第二个目标 (g2: 燃油消耗)

    # 创建图形
    plt.figure(figsize=(10, 7))
    plt.scatter(
        taxiing_time_values,
        fuel_consumption_values,
        c='blue',
        label=f'Generation {generation+1}',
        alpha=0.7,
        edgecolors='k',
        s=50  # 点的大小
    )

    # 添加标签和标题
    plt.xlabel("Total Taxiing Time (seconds)", fontsize=12)
    plt.ylabel("Total Fuel Consumption (kg)", fontsize=12)
    plt.title(f"Pareto Front - Generation {generation+1}", fontsize=14)
    plt.legend(fontsize=10)
    plt.grid(True, linestyle='--', alpha=0.7)

    # 保存图片
    os.makedirs(output_folder, exist_ok=True)  # 确保输出文件夹存在
    pf_image_path = os.path.join(output_folder, f"PF_Run{run_index}_Gen{generation+1}.png")
    plt.savefig(pf_image_path, dpi=300, bbox_inches='tight')
    plt.close()  # 关闭图像，避免内存占用

    print(f"Generation {generation+1}: Pareto front image saved at {pf_image_path}")


def build_aircraft_subgraph(airport_graph, start_node, end_node, nodes_df):
    """
    构建飞机的可行滑行子图

    通过查找从起点到终点的所有简单路径，构建飞机可用的子图。
    过滤掉包含不允许通过的节点(如跑道或登机口)的路径。

    参数:
        airport_graph: 机场完整图
        start_node: 起始节点
        end_node: 终止节点
        nodes_df: 节点数据DataFrame

    返回:
        aircraft_subgraph: 飞机可用的子图
    """
    # 创建图的副本，避免修改原图
    graph_copy = airport_graph.copy()

    try:
        # 查找起点到终点的所有简单路径
        all_paths = list(nx.all_simple_paths(graph_copy, source=start_node, target=end_node))

        # 获取路径中涉及的所有节点和边
        valid_nodes = set()
        valid_edges = set()

        # 遍历所有路径，过滤无效路径
        for path in all_paths:
            is_valid_path = True
            filtered_nodes = []

            # 检查路径中的每个节点
            for node in path:
                # 获取节点的类型规格
                node_spec = nodes_df.loc[nodes_df['Node ID'] == node, 'Specification'].values[0]

                # 如果节点不是起点或终点，且是跑道或登机口，则路径无效
                if (node != start_node and node != end_node and
                    node_spec in ['runway', 'gate']):
                    is_valid_path = False
                    break
                else:
                    filtered_nodes.append(node)

            # 如果路径有效，添加其节点和边到有效集合
            if is_valid_path:
                valid_nodes.update(filtered_nodes)
                # 添加路径中的边
                for i in range(len(filtered_nodes) - 1):
                    valid_edges.add((filtered_nodes[i], filtered_nodes[i + 1]))

        # 创建新的子图
        subgraph = nx.Graph()

        # 添加有效节点和边
        subgraph.add_nodes_from(valid_nodes)
        subgraph.add_edges_from(valid_edges)

        # 如果子图为空或不包含起点和终点，抛出异常
        if not subgraph.has_node(start_node) or not subgraph.has_node(end_node):
            raise nx.NetworkXNoPath(f"No valid path from {start_node} to {end_node}")

        return subgraph

    except Exception as e:
        print(f"Error building subgraph from {start_node} to {end_node}: {e}")
        # 创建一个最小子图，只包含起点和终点
        minimal_subgraph = nx.Graph()
        minimal_subgraph.add_nodes_from([start_node, end_node])
        return minimal_subgraph


def initialize_population(node_vectors, min_path_lengths, population_size, random_factor):
    """
    初始化种群

    为每架飞机创建初始路径编码，包括:
    - 滑行起始时间 (初始为0)
    - 每个节点的M1值 (影响路径选择)
    - 每个节点的M2值 (影响速度选择)

    参数:
        node_vectors: 每架飞机的节点向量
        min_path_lengths: 每个节点到终点的最短路径长度
        population_size: 种群大小
        random_factor: 随机因子，用于M1值的计算

    返回:
        initial_population: 初始种群
    """
    initial_population = []

    # 生成指定大小的种群
    for _ in range(population_size):
        individual = {}

        # 为每架飞机创建路径编码
        for aircraft_id, node_vector in node_vectors.items():
            # 初始化飞机的路径编码，第一个元素为滑行起始时间(初始为0)
            aircraft_path = [0]

            # 为每个节点生成(M1,M2)编码对
            for node in node_vector:
                # M1值影响路径选择，基于到终点的最短路径长度加上随机扰动
                m1_value = -min_path_lengths[(aircraft_id, node)] + random.uniform(0, random_factor)

                # M2值影响速度选择，在[0,1]范围内随机生成
                m2_value = random.uniform(0, 1)

                # 添加节点的编码对
                aircraft_path.append((m1_value, m2_value))

            # 将飞机的路径编码添加到个体中
            individual[aircraft_id] = aircraft_path

        # 将个体添加到种群
        initial_population.append(individual)

    return initial_population


def single_objective_environmental_selection(population, evaluated_population, population_size, objective_index):
    """
    单目标环境选择函数
    
    根据指定的目标函数索引对种群进行排序选择，用于单目标优化预热。
    
    参数:
        population: 当前种群
        evaluated_population: 种群的适应度值列表
        population_size: 目标种群大小
        objective_index: 目标函数索引 (0=总滑行时间g1, 1=总燃油消耗g2)
    
    返回:
        selected_population: 选择后的种群
        selected_fitness: 选择后的适应度值
    """
    # 将种群与适应度配对并按指定目标排序
    paired_data = list(zip(population, evaluated_population))
    # 按指定目标函数值升序排序（越小越好）
    sorted_data = sorted(paired_data, key=lambda x: x[1][objective_index])
    
    # 选择前population_size个最优个体
    selected_data = sorted_data[:population_size]
    
    # 分离种群和适应度
    selected_population = [item[0] for item in selected_data]
    selected_fitness = [item[1] for item in selected_data]
    
    return selected_population, selected_fitness


def single_objective_optimizer(node_vectors, min_path_lengths, aircraft_df, aircraft_subgraphs, 
                             nodes_df, edges_df, speed_profile_df, max_cost, airport_graph,
                             objective_index, population_size=50, generations=20, 
                             crossover_prob=0.8, mutation_prob=0.15, random_factor=4.5):
    """
    单目标优化器
    
    针对指定的单个目标函数执行进化算法优化，用于种群预热初始化。
    
    参数:
        node_vectors: 每架飞机的节点向量
        min_path_lengths: 每个节点到终点的最短路径长度
        aircraft_df: 飞机数据DataFrame
        aircraft_subgraphs: 每架飞机的子图
        nodes_df: 节点数据DataFrame
        edges_df: 边数据DataFrame
        speed_profile_df: 速度配置数据DataFrame
        max_cost: 最大惩罚成本字典
        airport_graph: 机场图
        objective_index: 优化的目标函数索引 (0=总滑行时间g1, 1=总燃油消耗g2)
        population_size: 单目标优化种群大小 (默认50)
        generations: 单目标优化代数 (默认20)
        crossover_prob: 交叉概率 (默认0.8)
        mutation_prob: 变异概率 (默认0.15)
        random_factor: 随机因子 (默认4.5)
    
    返回:
        best_population: 最优的种群
        best_fitness: 最优种群的适应度值
    """
    objective_names = ["总滑行时间(g1)", "总燃油消耗(g2)"]
    objective_units = ["秒", "kg"]
    print(f"  启动单目标优化器 - 目标: {objective_names[objective_index]}")
    print(f"  优化参数: 种群={population_size}, 代数={generations}, 交叉概率={crossover_prob}, 变异概率={mutation_prob}")
    
    # 初始化种群 - 使用原始随机初始化方法
    population = initialize_population(node_vectors, min_path_lengths, population_size, random_factor)
    
    # 评估初始种群
    evaluated_population, repair_population = evaluate_population(
        copy.deepcopy(population),
        aircraft_df,
        aircraft_subgraphs,
        node_vectors,
        min_path_lengths,
        nodes_df,
        edges_df,
        speed_profile_df,
        max_cost,
        copy.deepcopy(airport_graph)
    )
    population = copy.deepcopy(repair_population)
    
    # 使用单目标选择对初始种群排序
    population, evaluated_population = single_objective_environmental_selection(
        population, evaluated_population, population_size, objective_index
    )
    
    best_fitness_history = []
    avg_fitness_history = []
    improvement_count = 0
    stagnation_count = 0
    
    # 初始最佳值
    initial_best = evaluated_population[0][objective_index]
    current_best = initial_best
    
    print(f"  初始最佳{objective_names[objective_index]}: {initial_best:.6f} {objective_units[objective_index]}")
    print("  " + "="*50)
    
    # 进化迭代
    for generation in range(generations):
        # 交叉和变异
        offspring_population = crossover_and_mutate(
            copy.deepcopy(population),
            crossover_prob,
            mutation_prob
        )
        
        # 评估子代
        evaluated_offspring, repair_offspring = evaluate_population(
            copy.deepcopy(offspring_population),
            aircraft_df,
            aircraft_subgraphs,
            node_vectors,
            copy.deepcopy(min_path_lengths),
            nodes_df,
            edges_df,
            speed_profile_df,
            max_cost,
            copy.deepcopy(airport_graph)
        )
        offspring_population = copy.deepcopy(repair_offspring)
        
        # 合并父代和子代
        combined_population = population + offspring_population
        combined_fitness = evaluated_population + evaluated_offspring
        
        # 单目标环境选择
        population, evaluated_population = single_objective_environmental_selection(
            combined_population, 
            combined_fitness, 
            population_size, 
            objective_index
        )
        
        # 记录统计信息
        current_best = evaluated_population[0][objective_index]
        avg_fitness = np.mean([fitness[objective_index] for fitness in evaluated_population])
        best_fitness_history.append(current_best)
        avg_fitness_history.append(avg_fitness)
        
        # 检查改善情况
        if generation > 0:
            if current_best < best_fitness_history[-2]:  # 目标函数越小越好
                improvement_count += 1
                stagnation_count = 0
            else:
                stagnation_count += 1
        
        # 计算改善量
        improvement = initial_best - current_best
        improvement_percent = (improvement / initial_best) * 100 if initial_best > 0 else 0
        
        # 详细输出（每代都输出）
        print(f"  第{generation+1:2d}代: 最佳={current_best:.6f}, 平均={avg_fitness:.6f}, 改善={improvement:.6f}({improvement_percent:+.2f}%)")
        
        # 每5代输出更详细的统计
        if (generation + 1) % 5 == 0:
            recent_improvement = best_fitness_history[max(0, generation-4)] - current_best if generation >= 4 else improvement
            print(f"    >> 近5代改善: {recent_improvement:.6f}, 改善次数: {improvement_count}, 停滞次数: {stagnation_count}")
    
    # 最终统计
    final_improvement = initial_best - current_best
    final_improvement_percent = (final_improvement / initial_best) * 100 if initial_best > 0 else 0
    
    print("  " + "="*50)
    print(f"  单目标优化完成:")
    print(f"    初始最佳: {initial_best:.6f} {objective_units[objective_index]}")
    print(f"    最终最佳: {current_best:.6f} {objective_units[objective_index]}")
    print(f"    总改善量: {final_improvement:.6f} {objective_units[objective_index]} ({final_improvement_percent:+.2f}%)")
    print(f"    改善代数: {improvement_count}/{generations} ({improvement_count/generations*100:.1f}%)")
    
    # 分析收敛性
    if final_improvement > 0:
        print(f"    ✅ 优化成功 - 找到了更好的解")
    elif final_improvement == 0:
        print(f"    ⚠️  优化停滞 - 目标函数值未改善")
    else:
        print(f"    ❌ 优化失败 - 目标函数值变差")
    
    # 分析收敛趋势
    if len(best_fitness_history) >= 10:
        early_avg = np.mean(best_fitness_history[:5])
        late_avg = np.mean(best_fitness_history[-5:])
        trend_improvement = early_avg - late_avg
        if trend_improvement > final_improvement * 0.8:
            print(f"    📈 收敛良好 - 持续改善")
        elif stagnation_count >= generations * 0.3:
            print(f"    📊 早期收敛 - 可考虑增加代数或调整参数")
        else:
            print(f"    📉 收敛缓慢 - 可考虑调整交叉变异参数")
    
    return population, evaluated_population


def generate_knowledge_base_solutions(node_vectors, min_path_lengths, 
                                     aircraft_df, aircraft_subgraphs, nodes_df, edges_df, 
                                     speed_profile_df, max_cost, airport_graph,
                                     warmup_generations=20, warmup_population_size=50,
                                     config_name='standard'):
    """
    生成知识库初始化用的高质量解
    
    通过单目标优化生成高质量解，专门用于知识库初始化，与种群初始化完全分离。
    
    改进逻辑：
    1. 执行两次单目标优化，分别针对总滑行时间和总燃油消耗
    2. 返回所有优化结果，用于知识库初始化
    3. 种群初始化与此完全分离，总是使用随机初始化
    
    参数:
        node_vectors: 每架飞机的节点向量
        min_path_lengths: 每个节点到终点的最短路径长度
        aircraft_df: 飞机数据DataFrame  
        aircraft_subgraphs: 每架飞机的子图
        nodes_df: 节点数据DataFrame
        edges_df: 边数据DataFrame
        speed_profile_df: 速度配置数据DataFrame
        max_cost: 最大惩罚成本字典
        airport_graph: 机场图
        warmup_generations: 单目标优化代数 (默认20)
        warmup_population_size: 单目标优化种群大小 (默认50)
        config_name: 配置名称，用于自动调整优化参数 (默认'standard')
    
    返回:
        knowledge_solutions: 用于知识库的高质量解列表 [(individual, fitness), ...]
    """
    print("开始生成知识库初始化解...")
    
    # 根据配置名称自动调整优化参数
    optimization_params = {
        'fast': {'crossover_prob': 0.7, 'mutation_prob': 0.1},
        'standard': {'crossover_prob': 0.8, 'mutation_prob': 0.15},
        'high_quality': {'crossover_prob': 0.85, 'mutation_prob': 0.2},
        'premium': {'crossover_prob': 0.9, 'mutation_prob': 0.25},
        'balanced': {'crossover_prob': 0.8, 'mutation_prob': 0.15},
        'large_scale': {'crossover_prob': 0.85, 'mutation_prob': 0.2},
        'debug': {'crossover_prob': 0.9, 'mutation_prob': 0.3},
        'aggressive': {'crossover_prob': 0.95, 'mutation_prob': 0.4}
    }
    
    # 获取优化参数，默认使用standard配置
    params = optimization_params.get(config_name, optimization_params['standard'])
    crossover_prob = params['crossover_prob']
    mutation_prob = params['mutation_prob']
    
    print(f"  使用配置: {config_name}")
    print(f"  优化参数: 交叉概率={crossover_prob}, 变异概率={mutation_prob}")
    print(f"  单目标优化: 种群大小={warmup_population_size}, 代数={warmup_generations}")
    
    knowledge_solutions = []
    
    # 1. 针对总滑行时间(g1)的单目标优化
    print("\n步骤1: 针对总滑行时间(g1)执行单目标优化...")
    g1_population, g1_fitness = single_objective_optimizer(
        node_vectors, min_path_lengths, aircraft_df, aircraft_subgraphs,
        nodes_df, edges_df, speed_profile_df, max_cost, airport_graph,
        objective_index=0,  # g1: 总滑行时间
        population_size=warmup_population_size,
        generations=warmup_generations,
        crossover_prob=crossover_prob,
        mutation_prob=mutation_prob
    )
    
    # 将g1优化结果添加到知识解集合
    for ind, fit in zip(g1_population, g1_fitness):
        knowledge_solutions.append((copy.deepcopy(ind), fit))
    
    print(f"  g1优化完成，获得{len(g1_population)}个解，最佳时间: {g1_fitness[0][0]:.6f}秒")
    
    # 2. 针对总燃油消耗(g2)的单目标优化  
    print("\n步骤2: 针对总燃油消耗(g2)执行单目标优化...")
    g2_population, g2_fitness = single_objective_optimizer(
        node_vectors, min_path_lengths, aircraft_df, aircraft_subgraphs,
        nodes_df, edges_df, speed_profile_df, max_cost, airport_graph,
        objective_index=1,  # g2: 总燃油消耗
        population_size=warmup_population_size,
        generations=warmup_generations,
        crossover_prob=crossover_prob,
        mutation_prob=mutation_prob
    )
    
    # 将g2优化结果添加到知识解集合
    for ind, fit in zip(g2_population, g2_fitness):
        knowledge_solutions.append((copy.deepcopy(ind), fit))
    
    print(f"  g2优化完成，获得{len(g2_population)}个解，最佳燃油: {g2_fitness[0][1]:.6f}kg")
    
    # 3. 输出统计信息
    print("\n" + "="*60)
    print("知识库解生成完成!")
    print(f"  总计生成: {len(knowledge_solutions)}个高质量解")
    print(f"  来源分布: {len(g1_population)}个g1优化解 + {len(g2_population)}个g2优化解")
    print(f"  g1最佳解: 时间={g1_fitness[0][0]:.6f}秒, 燃油={g1_fitness[0][1]:.6f}kg")
    print(f"  g2最佳解: 时间={g2_fitness[0][0]:.6f}秒, 燃油={g2_fitness[0][1]:.6f}kg")
    print(f"  优化参数: 交叉概率={crossover_prob}, 变异概率={mutation_prob}")
    print("  注意: 这些解将专门用于知识库初始化，种群将采用独立的随机初始化")
    print("="*60)
    
    return knowledge_solutions


# 保留原有的函数名作为向后兼容，但修改其行为
def initialize_population_with_warmup(node_vectors, min_path_lengths, population_size, random_factor,
                                    aircraft_df, aircraft_subgraphs, nodes_df, edges_df, 
                                    speed_profile_df, max_cost, airport_graph,
                                    warmup_generations=20, warmup_population_size=50,
                                    elite_count_per_objective=30, config_name='standard'):
    """
    改进后的种群初始化策略（向后兼容接口）
    
    在新的分离式初始化逻辑下，这个函数现在只负责生成随机种群。
    知识库的初始化通过单独的单目标优化过程完成。
    
    注意：这个函数现在忽略elite_count_per_objective参数，
    因为种群初始化和知识库初始化已经完全分离。
    """
    print("🔄 改进提示: 使用分离式初始化策略")
    print("  - 知识库: 从单目标优化获取高质量解")
    print("  - 种群: 完全随机初始化保证多样性")
    print("  - 优势: 避免重复个体注入失败，增强引导效果")
    
    # 现在总是返回完全随机的种群
    print(f"\n生成随机种群(大小: {population_size})...")
    random_population = initialize_population(node_vectors, min_path_lengths, population_size, random_factor)
    
    print("✅ 随机种群生成完成!")
    print(f"  种群大小: {len(random_population)}")
    print("  组成: 100% 随机个体 (保证最大多样性)")
    print("  注意: 知识库将通过独立的单目标优化过程初始化")
    
    return random_population


def MARMT_RK_global(aircraft_df, airport_graph, nodes_df, edges_df, speed_profile_df, run_index, output_folder,
                   use_warmup_initialization=False, warmup_config=None):
    """
    多目标机场滑行路径规划算法主函数

    执行基于NSGA-II的多目标优化算法，寻找最优的滑行路径集合。

    参数:
        aircraft_df: 飞机数据DataFrame
        airport_graph: 机场完整图
        nodes_df: 节点数据DataFrame
        edges_df: 边数据DataFrame
        speed_profile_df: 速度配置数据DataFrame
        run_index: 运行索引
        output_folder: 输出文件夹路径
        use_warmup_initialization: 是否使用单目标优化预热初始化 (默认False)
        warmup_config: 预热初始化配置字典 (可选)
                      可包含: warmup_generations, warmup_population_size, elite_count_per_objective

    返回:
        pareto_front: 最终帕累托前沿
        pareto_set: 最终帕累托集
    """
    # 设置预热初始化的默认配置
    default_warmup_config = {
        'warmup_generations': 20,           # 单目标优化代数
        'warmup_population_size': 50,       # 单目标优化种群大小  
        'elite_count_per_objective': 30     # 每个目标选取的精英个体数
    }
    
    # 合并用户配置和默认配置
    if warmup_config is None:
        warmup_config = {}
    final_warmup_config = {**default_warmup_config, **warmup_config}
    
    # 输出初始化策略信息
    if use_warmup_initialization:
        print("=" * 60)
        print("使用单目标优化预热初始化策略")
        print(f"配置: 预热代数={final_warmup_config['warmup_generations']}, " +
              f"预热种群大小={final_warmup_config['warmup_population_size']}, " +
              f"每目标精英数={final_warmup_config['elite_count_per_objective']}")
        print("=" * 60)
    else:
        print("=" * 60)
        print("使用传统随机初始化策略")  
        print("=" * 60)

    # 添加飞机ID列
    aircraft_df['Aircraft ID'] = aircraft_df.index

    print(f"开始为{len(aircraft_df)}架飞机构建子图...")

    # 为每架飞机构建可行滑行子图
    aircraft_subgraphs = {}
    for _, aircraft in aircraft_df.iterrows():
        aircraft_id = aircraft['Aircraft ID']
        start_node = aircraft['Start Node']
        end_node = aircraft['End Node']

        # 构建子图
        subgraph = build_aircraft_subgraph(
            airport_graph,
            start_node,
            end_node,
            nodes_df
        )
        aircraft_subgraphs[aircraft_id] = subgraph

    print("子图构建完成，准备提取节点向量和计算最短路径...")

    # 初始化节点向量和最短路径长度字典
    node_vectors = {}
    min_path_lengths = {}

    # 为每架飞机提取节点向量并计算最短路径长度
    for _, aircraft in aircraft_df.iterrows():
        aircraft_id = aircraft['Aircraft ID']
        start_node = aircraft['Start Node']
        end_node = aircraft['End Node']
        subgraph = aircraft_subgraphs[aircraft_id]

        # 提取所有节点
        all_nodes = list(subgraph.nodes)

        # 构建节点向量：起点 + 中间节点 + 终点
        node_vector = [start_node]
        node_vector.extend([node for node in all_nodes
                           if node != start_node and node != end_node])
        node_vector.append(end_node)

        # 存储节点向量
        node_vectors[aircraft_id] = node_vector

        # 计算每个节点到终点的最短路径长度
        for node in subgraph.nodes:
            try:
                # 使用NetworkX计算最短路径长度
                path_length = nx.shortest_path_length(subgraph, source=node, target=end_node)
                min_path_lengths[(aircraft_id, node)] = path_length
            except nx.NetworkXNoPath:
                # 如果没有路径，设置为无穷大
                min_path_lengths[(aircraft_id, node)] = float('inf')

    print("节点向量和最短路径计算完成，设置算法参数...")

    # 算法参数设置
    popsize = 100  # 种群大小
    Max_generation = 100  # 最大代数
    rtmax = 4.5  # 随机因子上限
    pc = 0.6  # 交叉概率
    pm = 0.1  # 变异概率

    # 计算最大惩罚成本
    # 假设最长路径长度为200
    longest_path_length = 200

    # 速度和加速度参数
    cruise_speed = 5.14  # 匀速滑行速度 (m/s)
    acceleration = 0.98  # 加速度 (m/s^2)

    # 计算各阶段时间和距离
    accel_time = cruise_speed / acceleration  # 加速时间
    accel_distance = (0 + cruise_speed) / 2 * accel_time  # 加速阶段距离

    decel_time = cruise_speed / acceleration  # 减速时间
    decel_distance = (cruise_speed / 2) * decel_time  # 减速阶段距离

    # 计算匀速阶段
    cruise_distance = longest_path_length - (accel_distance + decel_distance)
    cruise_time = cruise_distance / cruise_speed

    # 计算总滑行时间和燃油消耗
    total_max_time = accel_time + cruise_time + decel_time
    max_fuel_flow = 0.724  # 最大燃油流量 (kg/s)
    total_max_fuel = total_max_time * max_fuel_flow

    # 创建最大成本字典
    max_Cost = {
        'max_time': round(total_max_time, 2),
        'max_fuel': round(total_max_fuel, 2)
    }

    print(f"算法参数设置完成，最大惩罚时间: {max_Cost['max_time']}秒，最大惩罚燃油: {max_Cost['max_fuel']}kg")

    print("初始化种群...")
    # 根据开关选择初始化策略
    if use_warmup_initialization:
        # 从配置中提取配置名称，如果没有则使用'standard'
        config_name = final_warmup_config.get('config_name', 'standard')
        
        # 使用单目标优化预热初始化
        population = initialize_population_with_warmup(
            node_vectors, min_path_lengths, popsize, rtmax,
            aircraft_df, aircraft_subgraphs, nodes_df, edges_df,
            speed_profile_df, max_Cost, airport_graph,
            warmup_generations=final_warmup_config['warmup_generations'],
            warmup_population_size=final_warmup_config['warmup_population_size'], 
            elite_count_per_objective=final_warmup_config['elite_count_per_objective'],
            config_name=config_name
        )
    else:
        # 使用传统随机初始化
        population = initialize_population(node_vectors, min_path_lengths, popsize, rtmax)

    # 存储每代的帕累托前沿和帕累托集
    all_PF = []
    all_PS = []

    print("评估初始种群...")
    # 对初始种群进行评价
    evaluated_population, repair_population = evaluate_population(
        copy.deepcopy(population),
        aircraft_df,
        aircraft_subgraphs,
        node_vectors,
        min_path_lengths,
        nodes_df,
        edges_df,
        speed_profile_df,
        max_Cost,
        copy.deepcopy(airport_graph)
    )
    population = copy.deepcopy(repair_population)

    # 迭代进行进化
    for generation in range(Max_generation):
        print(f"Generation {generation + 1}/{Max_generation}")

        # 进行交叉和变异
        offsprings_population = crossover_and_mutate(
            copy.deepcopy(population),
            pc,  # 交叉概率
            pm   # 变异概率
        )

        # 子代种群评价
        evaluated_offsprings, repair_offsprings_population = evaluate_population(
            copy.deepcopy(offsprings_population),
            aircraft_df,
            aircraft_subgraphs,
            node_vectors,
            copy.deepcopy(min_path_lengths),
            nodes_df,
            edges_df,
            speed_profile_df,
            max_Cost,
            copy.deepcopy(airport_graph)
        )
        offsprings_population = copy.deepcopy(repair_offsprings_population)

        # 环境选择 - 使用NSGA-II选择下一代种群
        population, evaluated_population, pareto_front, pareto_set = environmental_selection(
            copy.deepcopy(population),          # 父代种群
            copy.deepcopy(offsprings_population), # 子代种群
            copy.deepcopy(evaluated_population),  # 父代适应度
            copy.deepcopy(evaluated_offsprings),  # 子代适应度
            popsize                               # 种群大小
        )

        # 存储当前代的帕累托前沿和帕累托集
        all_PF.append(pareto_front)
        all_PS.append(pareto_set)

        # 绘制并保存当前代的帕累托前沿图
        plot_pareto_front(pareto_front, generation, output_folder, run_index)

        # 输出当前代的目标函数值范围
        if pareto_front:
            min_time = min(point[0] for point in pareto_front)
            max_time = max(point[0] for point in pareto_front)
            min_fuel = min(point[1] for point in pareto_front)
            max_fuel = max(point[1] for point in pareto_front)
            print(f"  目标函数范围 - 时间: [{min_time:.2f}, {max_time:.2f}], 燃油: [{min_fuel:.2f}, {max_fuel:.2f}]")
            print(f"  帕累托前沿大小: {len(pareto_front)}")

    # 创建输出文件夹（如果不存在）
    os.makedirs(output_folder, exist_ok=True)

    # 保存所有代的帕累托前沿和帕累托集
    output_file = os.path.join(output_folder, f"PF_PS_run_{run_index}.npz")
    np.savez(output_file, PF=np.array(all_PF, dtype=object), PS=np.array(all_PS, dtype=object))

    # 保存最终代的帕累托前沿和帕累托集为单独的文件
    final_pf_filename = os.path.join(output_folder, f"PF_run_{run_index}.npy")
    final_ps_filename = os.path.join(output_folder, f"PS_run_{run_index}.npy")
    np.save(final_pf_filename, np.array(pareto_front))
    np.save(final_ps_filename, np.array(pareto_set))

    print(f"运行 {run_index} 完成！")
    print(f"结果已保存到: {output_file}")
    print(f"最终帕累托前沿: {final_pf_filename}")
    print(f"最终帕累托集: {final_ps_filename}")

    # 返回最终的帕累托前沿和帕累托集
    return pareto_front, pareto_set


def MARMT_RK_global_with_akid(aircraft_df, airport_graph, nodes_df, edges_df, speed_profile_df, 
                             run_index, output_folder, use_akid=True, akid_config=None,
                             use_warmup_initialization=False, warmup_config=None):
    """
    集成AKID的多目标机场滑行路径规划算法主函数

    执行基于AKID-NSGA-II的多目标优化算法，寻找最优的滑行路径集合。

    参数:
        aircraft_df: 飞机数据DataFrame
        airport_graph: 机场完整图
        nodes_df: 节点数据DataFrame
        edges_df: 边数据DataFrame
        speed_profile_df: 速度配置数据DataFrame
        run_index: 运行索引
        output_folder: 输出文件夹路径
        use_akid: 是否启用AKID增强 (默认True)
        akid_config: AKID配置字典
        use_warmup_initialization: 是否使用单目标优化预热初始化 (默认False)
        warmup_config: 预热初始化配置字典 (可选)

    返回:
        pareto_front: 最终帕累托前沿
        pareto_set: 最终帕累托集
        akid_report: AKID算法执行报告
    """
    # 导入AKID模块
    if use_akid:
        from akid_modules.core import DynamicKnowledgeBase, DiversityMonitor
        from akid_modules.injection import KnowledgeInjector
        from akid_modules.segments import PathSegmentAnalyzer
        from akid_modules.utils import (setup_akid_logger, validate_akid_config, 
                                       create_akid_summary_report, akid_profiler)
        from config import get_akid_config
        
        # 设置AKID日志记录器
        logger = setup_akid_logger()
        
        # 获取和验证AKID配置
        if akid_config is None:
            akid_config = get_akid_config('default')
        
        # 验证配置
        is_valid, errors = validate_akid_config(akid_config)
        if not is_valid:
            logger.warning(f"AKID配置验证失败: {errors}")
            logger.info("回退到标准NSGA-II算法")
            return MARMT_RK_global(aircraft_df, airport_graph, nodes_df, edges_df, 
                                 speed_profile_df, run_index, output_folder,
                                 use_warmup_initialization, warmup_config)
        
        # 初始化AKID模块
        logger.info("初始化AKID模块...")
        
        # 动态知识库
        dkb_config = akid_config.get('dkb_config', {})
        knowledge_base = DynamicKnowledgeBase(
            capacity=dkb_config.get('capacity', 100),
            elite_ratio=dkb_config.get('elite_ratio', 0.3),
            quality_weight=dkb_config.get('quality_weight', 0.7),
            auto_cleanup=dkb_config.get('auto_cleanup', True)
        )
        
        # 多样性监控
        dcmm_config = akid_config.get('dcmm_config', {})
        diversity_monitor = DiversityMonitor(
            window_size=dcmm_config.get('window_size', 5),
            stagnation_threshold=dcmm_config.get('stagnation_threshold', 3),
            diversity_threshold=dcmm_config.get('diversity_threshold', 0.3),
            convergence_threshold=dcmm_config.get('convergence_threshold', 0.001)
        )
        
        # 知识注入器
        akim_config = akid_config.get('akim_config', {})
        knowledge_injector = KnowledgeInjector(
            min_injection_ratio=akim_config.get('injection_ratio_range', (0.05, 0.2))[0],
            max_injection_ratio=akim_config.get('injection_ratio_range', (0.05, 0.2))[1],
            adaptation_learning_rate=akim_config.get('adaptation_learning_rate', 0.1),
            success_threshold=akim_config.get('success_threshold', 0.7)
        )
        
        # 路径段分析器
        segment_config = akid_config.get('key_segment_config', {})
        segment_analyzer = PathSegmentAnalyzer(
            min_length=segment_config.get('min_length', 3),
            min_frequency=segment_config.get('min_frequency', 3),
            quality_threshold=segment_config.get('quality_threshold', 0.5),
            similarity_threshold=segment_config.get('similarity_threshold', 0.7),
            max_segments_per_aircraft=segment_config.get('max_segments_per_aircraft', 10)
        )
        
        logger.info("AKID模块初始化完成")
    else:
        # 如果不使用AKID，直接调用原始算法
        return MARMT_RK_global(aircraft_df, airport_graph, nodes_df, edges_df, 
                             speed_profile_df, run_index, output_folder,
                             use_warmup_initialization, warmup_config)

    # 设置预热初始化的默认配置
    default_warmup_config = {
        'warmup_generations': 20,           # 单目标优化代数
        'warmup_population_size': 50,       # 单目标优化种群大小  
        'elite_count_per_objective': 30     # 每个目标选取的精英个体数
    }
    
    # 合并用户配置和默认配置
    if warmup_config is None:
        warmup_config = {}
    final_warmup_config = {**default_warmup_config, **warmup_config}
    
    # 输出初始化策略信息
    algorithm_name = "AKID-NSGA2" if use_akid else "NSGA-II"
    if use_warmup_initialization:
        print("=" * 60)
        print(f"使用{algorithm_name}算法 + 单目标优化预热初始化策略")
        print(f"配置: 预热代数={final_warmup_config['warmup_generations']}, " +
              f"预热种群大小={final_warmup_config['warmup_population_size']}, " +
              f"每目标精英数={final_warmup_config['elite_count_per_objective']}")
        print("=" * 60)
    else:
        print("=" * 60)
        print(f"使用{algorithm_name}算法 + 传统随机初始化策略")  
        print("=" * 60)

    # 添加飞机ID列
    aircraft_df['Aircraft ID'] = aircraft_df.index

    print(f"开始为{len(aircraft_df)}架飞机构建子图...")

    # 为每架飞机构建可行滑行子图
    aircraft_subgraphs = {}
    for _, aircraft in aircraft_df.iterrows():
        aircraft_id = aircraft['Aircraft ID']
        start_node = aircraft['Start Node']
        end_node = aircraft['End Node']

        # 构建子图
        subgraph = build_aircraft_subgraph(
            airport_graph,
            start_node,
            end_node,
            nodes_df
        )
        aircraft_subgraphs[aircraft_id] = subgraph

    print("子图构建完成，准备提取节点向量和计算最短路径...")

    # 初始化节点向量和最短路径长度字典
    node_vectors = {}
    min_path_lengths = {}

    # 为每架飞机提取节点向量并计算最短路径长度
    for _, aircraft in aircraft_df.iterrows():
        aircraft_id = aircraft['Aircraft ID']
        start_node = aircraft['Start Node']
        end_node = aircraft['End Node']
        subgraph = aircraft_subgraphs[aircraft_id]

        # 提取所有节点
        all_nodes = list(subgraph.nodes)

        # 构建节点向量：起点 + 中间节点 + 终点
        node_vector = [start_node]
        node_vector.extend([node for node in all_nodes
                           if node != start_node and node != end_node])
        node_vector.append(end_node)

        # 存储节点向量
        node_vectors[aircraft_id] = node_vector

        # 计算每个节点到终点的最短路径长度
        for node in subgraph.nodes:
            try:
                # 使用NetworkX计算最短路径长度
                path_length = nx.shortest_path_length(subgraph, source=node, target=end_node)
                min_path_lengths[(aircraft_id, node)] = path_length
            except nx.NetworkXNoPath:
                # 如果没有路径，设置为无穷大
                min_path_lengths[(aircraft_id, node)] = float('inf')

    print("节点向量和最短路径计算完成，设置算法参数...")

    # 算法参数设置
    popsize = 100  # 种群大小
    Max_generation = 100  # 最大代数
    rtmax = 4.5  # 随机因子上限
    pc = 0.6  # 交叉概率
    pm = 0.1  # 变异概率

    # 计算最大惩罚成本
    # 假设最长路径长度为200
    longest_path_length = 200

    # 速度和加速度参数
    cruise_speed = 5.14  # 匀速滑行速度 (m/s)
    acceleration = 0.98  # 加速度 (m/s^2)

    # 计算各阶段时间和距离
    accel_time = cruise_speed / acceleration  # 加速时间
    accel_distance = (0 + cruise_speed) / 2 * accel_time  # 加速阶段距离

    decel_time = cruise_speed / acceleration  # 减速时间
    decel_distance = (cruise_speed / 2) * decel_time  # 减速阶段距离

    # 计算匀速阶段
    cruise_distance = longest_path_length - (accel_distance + decel_distance)
    cruise_time = cruise_distance / cruise_speed

    # 计算总滑行时间和燃油消耗
    total_max_time = accel_time + cruise_time + decel_time
    max_fuel_flow = 0.724  # 最大燃油流量 (kg/s)
    total_max_fuel = total_max_time * max_fuel_flow

    # 创建最大成本字典
    max_Cost = {
        'max_time': round(total_max_time, 2),
        'max_fuel': round(total_max_fuel, 2)
    }

    print(f"算法参数设置完成，最大惩罚时间: {max_Cost['max_time']}秒，最大惩罚燃油: {max_Cost['max_fuel']}kg")

    print("初始化种群...")
    # 根据开关选择初始化策略
    if use_warmup_initialization:
        # 从配置中提取配置名称，如果没有则使用'standard'
        config_name = final_warmup_config.get('config_name', 'standard')
        
        # 使用单目标优化预热初始化
        population = initialize_population_with_warmup(
            node_vectors, min_path_lengths, popsize, rtmax,
            aircraft_df, aircraft_subgraphs, nodes_df, edges_df,
            speed_profile_df, max_Cost, airport_graph,
            warmup_generations=final_warmup_config['warmup_generations'],
            warmup_population_size=final_warmup_config['warmup_population_size'], 
            elite_count_per_objective=final_warmup_config['elite_count_per_objective'],
            config_name=config_name
        )
    else:
        # 使用传统随机初始化
        population = initialize_population(node_vectors, min_path_lengths, popsize, rtmax)

    # 存储每代的帕累托前沿和帕累托集
    all_PF = []
    all_PS = []

    print("评估初始种群...")
    # 对初始种群进行评价
    evaluated_population, repair_population = evaluate_population(
        copy.deepcopy(population),
        aircraft_df,
        aircraft_subgraphs,
        node_vectors,
        min_path_lengths,
        nodes_df,
        edges_df,
        speed_profile_df,
        max_Cost,
        copy.deepcopy(airport_graph)
    )
    population = copy.deepcopy(repair_population)

    # AKID增强：初始化多样性监控和知识库
    if use_akid:
        logger.info("开始AKID增强的进化过程...")
        initial_objectives = [f[:2] for f in evaluated_population]  # 提取前两个目标
        diversity_monitor.update_metrics(initial_objectives, 0)
        
        # 根据初始化策略选择知识库初始化方式
        if use_warmup_initialization:
            logger.info("使用分离式初始化策略：知识库和种群完全分离")
            
            # 生成知识库初始化用的高质量解
            print("=" * 60)
            print("🔬 第一阶段：生成知识库初始化解")
            print("=" * 60)
            
            config_name = final_warmup_config.get('config_name', 'standard')
            knowledge_solutions = generate_knowledge_base_solutions(
                node_vectors, min_path_lengths, 
                aircraft_df, aircraft_subgraphs, nodes_df, edges_df,
                speed_profile_df, max_Cost, airport_graph,
                warmup_generations=final_warmup_config['warmup_generations'],
                warmup_population_size=final_warmup_config['warmup_population_size'],
                config_name=config_name
            )
            
            # 将高质量解添加到知识库
            kb_added = 0
            for individual, fitness in knowledge_solutions:
                if fitness[2] == 0:  # 只添加可行解
                    if knowledge_base.add_solution(individual, fitness[:2], 0):
                        kb_added += 1
            
            logger.info(f"知识库初始化完成：添加了 {kb_added} 个高质量解")
            
            # 第二阶段：生成完全随机的种群
            print("\n" + "=" * 60)
            print("🎲 第二阶段：生成随机种群（确保多样性）")
            print("=" * 60)
            
            logger.info("生成完全随机的种群以确保多样性...")
            population = initialize_population(node_vectors, min_path_lengths, popsize, rtmax)
            
            # 评估随机种群
            evaluated_population, repair_population = evaluate_population(
                copy.deepcopy(population),
                aircraft_df,
                aircraft_subgraphs,
                node_vectors,
                min_path_lengths,
                nodes_df,
                edges_df,
                speed_profile_df,
                max_Cost,
                copy.deepcopy(airport_graph)
            )
            population = copy.deepcopy(repair_population)
            
            # 更新多样性监控
            initial_objectives = [f[:2] for f in evaluated_population]
            diversity_monitor.update_metrics(initial_objectives, 0)
            
            print("\n✅ 分离式初始化完成!")
            print(f"  📚 知识库: {kb_added}个高质量优化解 (来自单目标优化)")
            print(f"  🎯 种群: {len(population)}个随机个体 (保证最大多样性)")
            print("  🚀 优势: 避免重复注入，增强引导效果")
            
            logger.info("完成分离式初始化：知识库包含优化解，种群保持随机多样性")
        else:
            # 传统方式：将初始种群的优秀解添加到知识库
            logger.info("使用传统方式初始化知识库：从初始种群添加优秀解...")
            for ind, fit in zip(population, evaluated_population):
                if fit[2] == 0:  # 只添加可行解
                    knowledge_base.add_solution(ind, fit[:2], 0)

    # 迭代进行进化
    for generation in range(Max_generation):
        print(f"Generation {generation + 1}/{Max_generation}")

        # 进行交叉和变异
        offsprings_population = crossover_and_mutate(
            copy.deepcopy(population),
            pc,  # 交叉概率
            pm   # 变异概率
        )

        # 子代种群评价
        evaluated_offsprings, repair_offsprings_population = evaluate_population(
            copy.deepcopy(offsprings_population),
            aircraft_df,
            aircraft_subgraphs,
            node_vectors,
            copy.deepcopy(min_path_lengths),
            nodes_df,
            edges_df,
            speed_profile_df,
            max_Cost,
            copy.deepcopy(airport_graph)
        )
        offsprings_population = copy.deepcopy(repair_offsprings_population)

        # 环境选择 - 使用NSGA-II选择下一代种群
        population, evaluated_population, pareto_front, pareto_set = environmental_selection(
            copy.deepcopy(population),          # 父代种群
            copy.deepcopy(offsprings_population), # 子代种群
            copy.deepcopy(evaluated_population),  # 父代适应度
            copy.deepcopy(evaluated_offsprings),  # 子代适应度
            popsize                               # 种群大小
        )

        # AKID增强处理
        if use_akid:
            import time
            start_time = time.time()
            
            # 1. 更新知识库
            for ind, fit in zip(pareto_set, pareto_front):
                if fit[2] == 0:  # 只添加可行解
                    knowledge_base.add_solution(ind, fit[:2], generation + 1)
            
            # 2. 更新多样性监控
            current_objectives = [f[:2] for f in evaluated_population]
            pre_injection_diversity = diversity_monitor.get_current_diversity()
            diversity_monitor.update_metrics(current_objectives, generation + 1)
            
            # 3. 判断是否需要知识注入
            should_inject, injection_reason = diversity_monitor.should_inject_knowledge(generation + 1)
            
            if should_inject:
                logger.info(f"代数 {generation + 1}: 触发知识注入 - 原因: {injection_reason}")
                
                # 执行知识注入
                population, evaluated_population, injection_stats = knowledge_injector.inject_knowledge(
                    population, evaluated_population, knowledge_base, diversity_monitor, 
                    generation + 1, injection_reason)
                
                # 评估注入成功率
                post_injection_diversity = diversity_monitor.get_current_diversity()
                injection_success = knowledge_injector.evaluate_injection_success(
                    pre_injection_diversity, post_injection_diversity)
                
                if injection_success:
                    diversity_monitor.reset_stagnation()
                    
                logger.info(f"知识注入完成: 注入 {injection_stats['injected_count']} 个解, "
                          f"成功率: {'成功' if injection_success else '失败'}")
            
            # 4. 关键子路径识别和应用（每20代执行一次）
            if generation > 0 and (generation + 1) % 20 == 0:
                logger.info(f"代数 {generation + 1}: 执行关键子路径分析...")
                
                # 提取关键路径段
                key_segments = segment_analyzer.extract_key_segments(knowledge_base, aircraft_df)
                
                if key_segments:
                    # 应用路径段增强
                    enhanced_population, enhancement_stats = segment_analyzer.apply_segment_enhancement(
                        population, aircraft_df, prob=0.2, strategy="adaptive")
                    
                    if enhancement_stats['success']:
                        # 重新评估增强后的种群
                        enhanced_fitness, enhanced_repaired = evaluate_population(
                            copy.deepcopy(enhanced_population),
                            aircraft_df, aircraft_subgraphs, node_vectors,
                            copy.deepcopy(min_path_lengths), nodes_df, edges_df,
                            speed_profile_df, max_Cost, copy.deepcopy(airport_graph)
                        )
                        
                        # 如果增强效果好，则使用增强后的种群
                        enhanced_objectives = [f[:2] for f in enhanced_fitness]
                        enhanced_diversity = diversity_monitor._calculate_diversity(np.array(enhanced_objectives))
                        
                        if enhanced_diversity > diversity_monitor.get_current_diversity():
                            population = enhanced_repaired
                            evaluated_population = enhanced_fitness
                            logger.info(f"路径段增强成功: 增强 {enhancement_stats['enhanced_individuals']} 个个体")
                        
            # 记录性能数据
            akid_time = time.time() - start_time
            akid_profiler.record_timing("AKID", "generation_enhancement", akid_time)

        # 存储当前代的帕累托前沿和帕累托集
        all_PF.append(pareto_front)
        all_PS.append(pareto_set)

        # 绘制并保存当前代的帕累托前沿图
        plot_pareto_front(pareto_front, generation, output_folder, run_index)

        # 输出当前代的目标函数值范围
        if pareto_front:
            min_time = min(point[0] for point in pareto_front)
            max_time = max(point[0] for point in pareto_front)
            min_fuel = min(point[1] for point in pareto_front)
            max_fuel = max(point[1] for point in pareto_front)
            print(f"  目标函数范围 - 时间: [{min_time:.2f}, {max_time:.2f}], 燃油: [{min_fuel:.2f}, {max_fuel:.2f}]")
            print(f"  帕累托前沿大小: {len(pareto_front)}")
            
            # AKID状态输出
            if use_akid and generation % 10 == 0:
                current_diversity = diversity_monitor.get_current_diversity()
                kb_stats = knowledge_base.get_statistics()
                print(f"  AKID状态 - 多样性: {current_diversity:.4f}, 知识库大小: {kb_stats['size']}/{kb_stats['capacity']}")

    # 创建输出文件夹（如果不存在）
    os.makedirs(output_folder, exist_ok=True)

    # 保存所有代的帕累托前沿和帕累托集
    output_file = os.path.join(output_folder, f"PF_PS_run_{run_index}.npz")
    np.savez(output_file, PF=np.array(all_PF, dtype=object), PS=np.array(all_PS, dtype=object))

    # 保存最终代的帕累托前沿和帕累托集为单独的文件
    final_pf_filename = os.path.join(output_folder, f"PF_run_{run_index}.npy")
    final_ps_filename = os.path.join(output_folder, f"PS_run_{run_index}.npy")
    np.save(final_pf_filename, np.array(pareto_front))
    np.save(final_ps_filename, np.array(pareto_set))

    # 生成AKID报告
    akid_report = None
    if use_akid:
        logger.info("生成AKID执行报告...")
        final_pareto_front_array = np.array([pf[:2] for pf in pareto_front]) if pareto_front else np.array([])
        akid_report = create_akid_summary_report(
            knowledge_base, diversity_monitor, knowledge_injector, 
            segment_analyzer, final_pareto_front_array)
        
        # 保存AKID报告
        akid_report_file = os.path.join(output_folder, f"AKID_report_run_{run_index}.json")
        from akid_modules.utils import save_akid_results
        save_akid_results(akid_report, akid_report_file, 'json')
        
        # 保存AKID模块数据
        if akid_config.get('monitoring_config', {}).get('save_injection_history', True):
            kb_file = os.path.join(output_folder, f"knowledge_base_run_{run_index}.json")
            knowledge_base.save_to_file(kb_file)
            
            segments_file = os.path.join(output_folder, f"key_segments_run_{run_index}.json")
            segment_analyzer.save_segments_to_file(segments_file)
        
        logger.info(f"AKID报告已保存到: {akid_report_file}")

    print(f"运行 {run_index} 完成！")
    print(f"结果已保存到: {output_file}")
    print(f"最终帕累托前沿: {final_pf_filename}")
    print(f"最终帕累托集: {final_ps_filename}")
    
    if use_akid:
        print(f"AKID报告: {akid_report_file}")

    # 返回最终的帕累托前沿、帕累托集和AKID报告
    return pareto_front, pareto_set, akid_report
