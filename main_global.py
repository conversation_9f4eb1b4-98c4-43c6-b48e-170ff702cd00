"""
机场滑行路径多目标优化主程序

本程序支持两种优化算法：
1. 标准NSGA-II算法 (MARMT_RK_global)
2. AKID-NSGA2增强算法 (MARMT_RK_global_with_akid)

主要目标是：
1. 最小化总滑行时间
2. 最小化总燃油消耗

程序从doh1.txt文件读取机场布局数据，包括节点、边和飞机信息，
然后使用选定的算法进行多次独立运行，并保存结果。
"""

import pandas as pd
import networkx as nx
import numpy as np
import os
from nsga2_core.algorithm import MARMT_RK_global, MARMT_RK_global_with_akid
from config import get_akid_config


def load_airport_data(file_path):
    """
    从文件加载机场布局数据

    参数:
        file_path: 数据文件路径

    返回:
        nodes_df: 节点数据DataFrame
        edges_df: 边数据DataFrame
        aircraft_df: 飞机数据DataFrame
    """
    # 读取机场布局数据
    with open(file_path, 'r') as file:
        lines = file.readlines()

    # 找到节点、边和飞机数据部分
    node_section_start = lines.index('%SECTION%1%;Nodes;\n') + 1
    edge_section_start = lines.index('%SECTION%2%;Edges;\n') + 1
    aircraft_section_start = lines.index('%SECTION%3%;Aircraft;\n') + 1

    # 解析节点数据
    node_data_lines = lines[node_section_start + 1:edge_section_start - 1]
    node_data = []
    for line in node_data_lines:
        if line.strip() and not line.startswith('%'):
            parts = line.strip().split(';')
            if len(parts) >= 4:
                node_id, x, y, specification = parts[1:5]
                node_data.append([node_id, float(x), float(y), specification])

    nodes_df = pd.DataFrame(node_data, columns=['Node ID', 'X', 'Y', 'Specification'])

    # 解析边数据
    edge_data_lines = lines[edge_section_start + 1:aircraft_section_start - 1]
    edge_data = []
    for line in edge_data_lines:
        if line.strip() and not line.startswith('%'):
            parts = line.strip().split(';')
            if len(parts) >= 6:
                edge_id, start_node, end_node, directed, length, edge_type = parts[1:7]
                # 初始状态下，时间窗为空或可通行
                unavailable_time_windows = []
                edge_data.append([edge_id, start_node, end_node, directed, float(length), edge_type, unavailable_time_windows])

    edges_df = pd.DataFrame(edge_data, columns=['Edge ID', 'Start Node', 'End Node', 'Directed', 'Length', 'Type', 'Unavailable Time Windows'])

    # 解析飞机数据
    aircraft_data_lines = lines[aircraft_section_start + 1:]
    aircraft_data = []
    for line in aircraft_data_lines:
        if line.strip() and not line.startswith('%'):
            parts = line.strip().split(';')
            if len(parts) >= 16:
                aircraft_type = parts[1].strip().strip("'")  # 去掉单引号
                start_node = parts[2].strip().strip("'")  # 去掉单引号
                end_node = parts[3].strip().strip("'")  # 去掉单引号
                start_time_str = parts[4].strip("[]")  # 去掉时间字段的方括号
                start_time_values = start_time_str.split(';')
                start_time = float(start_time_values[0].strip())  # 取第一个时间值
                weight_class = parts[16].strip().strip("'")
                aircraft_data.append([aircraft_type, start_node, end_node, start_time, weight_class])

    aircraft_df = pd.DataFrame(aircraft_data, columns=['Type', 'Start Node', 'End Node', 'Start Time', 'Weight Class'])
    # 对aircraft_df按照Start Time进行升序排序
    aircraft_df = aircraft_df.sort_values(by='Start Time').reset_index(drop=True)

    return nodes_df, edges_df, aircraft_df


def create_airport_graph(edges_df):
    """
    根据边数据创建机场无向图

    参数:
        edges_df: 边数据DataFrame

    返回:
        G: NetworkX无向图对象
    """
    # 创建无向图
    G = nx.Graph()

    # 添加节点和边，并包含时间窗信息
    for _, edge in edges_df.iterrows():
        start_node = edge['Start Node']
        end_node = edge['End Node']
        length = edge['Length']
        time_window = edge['Unavailable Time Windows']
        G.add_edge(start_node, end_node, length=length, unavailable_time_windows=time_window)
        G.add_edge(end_node, start_node, length=length, unavailable_time_windows=time_window)

    return G


def save_data_to_file(data, filename):
    """
    将数据保存为npy文件

    参数:
        data: 要保存的数据
        filename: 文件名
    """
    np.save(filename, np.array(data))


def main():
    """主函数"""
    # 简化配置 - 推荐设置
    # ==================================================
    
    print("🛩️ AKID-NSGA2 机场滑行路径优化系统")
    print("=" * 60)
    
    # 简单配置选项
    print("请选择运行模式:")
    print("1. 快速演示 (AKID-NSGA2, 1次运行)")
    print("2. 标准运行 (AKID-NSGA2, 5次运行)")
    print("3. 对比测试 (标准NSGA-II, 5次运行)")
    print("4. 完整评估 (AKID-NSGA2, 30次运行)")
    
    try:
        choice = input("\n请输入选择 (1-4，直接回车默认选择1): ").strip()
        if not choice:
            choice = "1"
    except:
        choice = "1"
    
    # 根据选择配置参数
    if choice == "1":
        USE_AKID_ENHANCEMENT = True
        NUM_RUNS = 1
        USE_WARMUP_INITIALIZATION = True
        print(f"\n✅ 选择: 快速演示模式")
    elif choice == "2":
        USE_AKID_ENHANCEMENT = True
        NUM_RUNS = 5
        USE_WARMUP_INITIALIZATION = True
        print(f"\n✅ 选择: 标准运行模式")
    elif choice == "3":
        USE_AKID_ENHANCEMENT = False
        NUM_RUNS = 5
        USE_WARMUP_INITIALIZATION = False
        print(f"\n✅ 选择: 对比测试模式 (标准NSGA-II)")
    elif choice == "4":
        USE_AKID_ENHANCEMENT = True
        NUM_RUNS = 30
        USE_WARMUP_INITIALIZATION = True
        print(f"\n✅ 选择: 完整评估模式")
    else:
        print(f"\n⚠️ 无效选择，使用默认设置 (快速演示)")
        USE_AKID_ENHANCEMENT = True
        NUM_RUNS = 1
        USE_WARMUP_INITIALIZATION = True
    
    # 固定的优化配置 (推荐设置)
    AKID_CONFIG_NAME = 'default'
    WARMUP_CONFIG = {
        'warmup_generations': 20,        # 单目标优化代数（用于知识库初始化）
        'warmup_population_size': 50,    # 单目标优化种群大小（用于知识库初始化）
        'elite_count_per_objective': 30  # 已废弃：分离式初始化中不再使用
    }
    
    # ==================================================
    
    # 获取AKID配置
    if USE_AKID_ENHANCEMENT:
        akid_config = get_akid_config(AKID_CONFIG_NAME)
    else:
        akid_config = None
    
    # 简化的配置信息输出
    algorithm_name = "AKID-NSGA2增强算法" if USE_AKID_ENHANCEMENT else "标准NSGA-II算法"
    if USE_WARMUP_INITIALIZATION:
        init_strategy = "分离式初始化（知识库获取优化解，种群保持随机）"
    else:
        init_strategy = "传统随机初始化"
    
    print(f"\n🔧 算法配置: {algorithm_name}")
    print(f"📊 初始化策略: {init_strategy}")
    print(f"🔄 运行次数: {NUM_RUNS}")
    if USE_WARMUP_INITIALIZATION:
        print(f"✨ 改进: 解决知识库重复注入问题，提高引导效果")
    print("=" * 60)

    # 读取机场布局数据
    airport_data_path = 'doh1.txt'
    print(f"📁 正在加载机场数据...")
    nodes_df, edges_df, aircraft_df = load_airport_data(airport_data_path)
    print(f"✅ 数据加载完成 - 节点:{len(nodes_df)}, 边:{len(edges_df)}, 飞机:{len(aircraft_df)}")

    # 读取速度配置数据
    speed_profile_path = 'doh1_database.csv'
    speed_profile_df = pd.read_csv(speed_profile_path)

    # 创建机场图
    airport_graph = create_airport_graph(edges_df)
    print(f"🗺️ 机场图构建完成 - 节点:{airport_graph.number_of_nodes()}, 边:{airport_graph.number_of_edges()}")

    # 创建输出文件夹（如果不存在）
    if USE_AKID_ENHANCEMENT:
        base_folder = 'results/akid_nsga2/'
        if USE_WARMUP_INITIALIZATION:
            output_folder = f'{base_folder}warmup_initialization_{AKID_CONFIG_NAME}/'
        else:
            output_folder = f'{base_folder}random_initialization_{AKID_CONFIG_NAME}/'
    else:
        base_folder = 'results/standard_nsga2/'
        if USE_WARMUP_INITIALIZATION:
            output_folder = f'{base_folder}warmup_initialization/'
        else:
            output_folder = f'{base_folder}random_initialization/'
    
    os.makedirs(output_folder, exist_ok=True)
    print(f"结果将保存到: {output_folder}")

    # 统计信息
    all_pareto_fronts = []
    all_pareto_sets = []
    all_akid_reports = []  # 存储AKID报告

    # 独立运行算法
    for run_index in range(1, NUM_RUNS + 1):
        print(f"\n开始第 {run_index}/{NUM_RUNS} 次运行...")

        # 根据配置选择算法
        if USE_AKID_ENHANCEMENT:
            # 使用AKID-NSGA2增强算法
            result = MARMT_RK_global_with_akid(
                aircraft_df,
                airport_graph,
                nodes_df,
                edges_df,
                speed_profile_df,
                run_index,
                output_folder,
                use_akid=True,
                akid_config=akid_config,
                use_warmup_initialization=USE_WARMUP_INITIALIZATION,
                warmup_config=WARMUP_CONFIG if USE_WARMUP_INITIALIZATION else None
            )
            
            # 解包结果
            if len(result) == 3:
                pareto_front, pareto_set, akid_report = result
                all_akid_reports.append(akid_report)
            else:
                pareto_front, pareto_set = result
                akid_report = None
        else:
            # 使用标准NSGA-II算法
            pareto_front, pareto_set = MARMT_RK_global(
                aircraft_df,
                airport_graph,
                nodes_df,
                edges_df,
                speed_profile_df,
                run_index,
                output_folder,
                use_warmup_initialization=USE_WARMUP_INITIALIZATION,
                warmup_config=WARMUP_CONFIG if USE_WARMUP_INITIALIZATION else None
            )

        # 收集结果用于统计
        all_pareto_fronts.append(pareto_front)
        all_pareto_sets.append(pareto_set)

        # 保存当前运行的帕累托前沿和帕累托集
        pf_filename = f"{output_folder}PF_run_{run_index}.npy"
        ps_filename = f"{output_folder}PS_run_{run_index}.npy"

        save_data_to_file(pareto_front, pf_filename)
        save_data_to_file(pareto_set, ps_filename)

        # 输出当前运行的统计信息
        if pareto_front:
            pf_size = len(pareto_front)
            min_time = min(point[0] for point in pareto_front)
            min_fuel = min(point[1] for point in pareto_front)
            print(f"运行 {run_index} 完成 - 帕累托前沿大小: {pf_size}, " +
                  f"最小时间: {min_time:.2f}, 最小燃油: {min_fuel:.2f}")
            
            # 输出AKID特定信息
            if USE_AKID_ENHANCEMENT and akid_report:
                injection_count = akid_report.get('injection_stats', {}).get('total_injections', 0)
                avg_diversity = akid_report.get('diversity_monitor_stats', {}).get('current_diversity', 0)
                print(f"  AKID统计 - 知识注入次数: {injection_count}, 平均多样性: {avg_diversity:.4f}")
        else:
            print(f"运行 {run_index} 完成 - 帕累托前沿为空")

    # 输出总体统计信息
    print("\n" + "=" * 80)
    algorithm_name = "AKID-NSGA2" if USE_AKID_ENHANCEMENT else "标准NSGA-II"
    print(f"所有运行完成！{algorithm_name}算法统计信息:")
    
    # 计算平均帕累托前沿大小
    pf_sizes = [len(pf) for pf in all_pareto_fronts if pf]
    if pf_sizes:
        avg_pf_size = np.mean(pf_sizes)
        std_pf_size = np.std(pf_sizes)
        print(f"• 平均帕累托前沿大小: {avg_pf_size:.2f} ± {std_pf_size:.2f}")
        print(f"• 帕累托前沿大小范围: [{min(pf_sizes)}, {max(pf_sizes)}]")
    
    # 计算目标函数统计
    all_times = []
    all_fuels = []
    for pf in all_pareto_fronts:
        if pf:
            all_times.extend([point[0] for point in pf])
            all_fuels.extend([point[1] for point in pf])
    
    if all_times and all_fuels:
        print(f"• 总滑行时间范围: [{min(all_times):.2f}, {max(all_times):.2f}]")
        print(f"• 总燃油消耗范围: [{min(all_fuels):.2f}, {max(all_fuels):.2f}]")
    
    # AKID特定统计
    if USE_AKID_ENHANCEMENT and all_akid_reports:
        # 统计AKID性能指标
        total_injections = sum(report.get('injection_stats', {}).get('total_injections', 0) 
                             for report in all_akid_reports if report)
        avg_total_injections = total_injections / len(all_akid_reports) if all_akid_reports else 0
        
        avg_diversities = [report.get('diversity_monitor_stats', {}).get('current_diversity', 0) 
                          for report in all_akid_reports if report]
        overall_avg_diversity = np.mean(avg_diversities) if avg_diversities else 0
        
        print(f"• AKID增强统计:")
        print(f"  - 平均知识注入次数: {avg_total_injections:.1f}")
        print(f"  - 平均多样性指标: {overall_avg_diversity:.4f}")
        
        # 保存综合AKID报告
        summary_report = {
            'algorithm': 'AKID-NSGA2',
            'configuration': AKID_CONFIG_NAME,
            'total_runs': NUM_RUNS,
            'average_injection_count': avg_total_injections,
            'average_diversity': overall_avg_diversity,
            'individual_reports': all_akid_reports
        }
        
        summary_file = os.path.join(output_folder, 'akid_summary_report.json')
        import json
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_report, f, indent=2, ensure_ascii=False)
        print(f"  - AKID综合报告: {summary_file}")
    
    print(f"• 结果保存路径: {output_folder}")
    print("=" * 80)


if __name__ == "__main__":
    main()
