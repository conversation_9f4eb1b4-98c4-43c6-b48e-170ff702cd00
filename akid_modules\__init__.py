"""
AKID-NSGA2 增强模块

本包实现了AKID-NSGA2算法的四大核心组件：
- 动态知识库(DKB)：存储和管理优秀解
- 多样性监控(DCMM)：监控种群多样性和收敛状态
- 知识注入(AKIM)：自适应知识注入策略
- 关键路径识别(KSPI)：识别和利用高频路径段
"""

from .core import DynamicKnowledgeBase, DiversityMonitor
from .injection import KnowledgeInjector
from .segments import PathSegmentAnalyzer
from .utils import (
    setup_akid_logger, 
    validate_akid_config, 
    create_akid_summary_report, 
    akid_profiler
)

__version__ = "1.0.0"
__author__ = "AKID-NSGA2 Development Team"

__all__ = [
    'DynamicKnowledgeBase',
    'DiversityMonitor', 
    'KnowledgeInjector',
    'PathSegmentAnalyzer',
    'setup_akid_logger',
    'validate_akid_config',
    'create_akid_summary_report',
    'akid_profiler'
] 