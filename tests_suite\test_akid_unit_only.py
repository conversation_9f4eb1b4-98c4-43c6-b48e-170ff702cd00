"""
AKID-NSGA2 纯单元测试 - 避免集成测试和GUI问题
"""
import unittest
import sys
import os
sys.path.append('.')

from akid_modules.core import DynamicKnowledgeBase, DiversityMonitor
from akid_modules.injection import KnowledgeInjector
from akid_modules.segments import PathSegmentAnalyzer
from akid_modules.utils import validate_akid_config, merge_akid_configs, AKIDProfiler
from config import get_akid_config, AKID_CONFIG

class TestDynamicKnowledgeBase(unittest.TestCase):
    """动态知识库(DKB)单元测试"""
    
    def setUp(self):
        """测试前准备"""
        self.knowledge_base = DynamicKnowledgeBase(capacity=10)
        
        # 测试数据
        self.test_individuals = [
            {'aircraft_1': [1, 2, 3], 'aircraft_2': [4, 5]},
            {'aircraft_1': [2, 3, 4], 'aircraft_2': [5, 6]},
            {'aircraft_1': [1, 3, 5], 'aircraft_2': [2, 4]}
        ]
        
        self.test_objectives = [
            (100.0, 50.0),
            (110.0, 48.0),
            (95.0, 52.0)
        ]
        
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.knowledge_base.capacity, 10)
        self.assertEqual(self.knowledge_base.get_size(), 0)
        self.assertTrue(self.knowledge_base.is_empty())
        
    def test_add_solution(self):
        """测试添加解到知识库"""
        self.knowledge_base.add_solution(
            self.test_individuals[0], 
            self.test_objectives[0], 
            generation=1
        )
        
        self.assertEqual(self.knowledge_base.get_size(), 1)
        self.assertFalse(self.knowledge_base.is_empty())
        
    def test_get_elite_solutions(self):
        """测试获取精英解"""
        # 添加测试数据
        for i, (ind, obj) in enumerate(zip(self.test_individuals, self.test_objectives)):
            self.knowledge_base.add_solution(ind, obj, i)
            
        elite_solutions = self.knowledge_base.get_elite_solutions(count=2)
        
        self.assertLessEqual(len(elite_solutions), 2)
        self.assertIsInstance(elite_solutions, list)

class TestDiversityMonitor(unittest.TestCase):
    """多样性监控(DCMM)单元测试"""
    
    def setUp(self):
        """测试前准备"""
        self.monitor = DiversityMonitor(window_size=3)
        
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.monitor.window_size, 3)
        
    def test_calculate_diversity(self):
        """测试多样性计算"""
        test_objectives = [(100.0, 50.0), (110.0, 48.0), (95.0, 52.0)]
        diversity = self.monitor.calculate_diversity(test_objectives)
        
        self.assertIsInstance(diversity, float)
        self.assertGreaterEqual(diversity, 0.0)
        
    def test_injection_decision(self):
        """测试注入决策"""
        # 测试低多样性触发注入
        should_inject = self.monitor.should_inject_knowledge(diversity=0.05, threshold=0.1)
        self.assertTrue(should_inject)
        
        # 测试高多样性不触发注入
        should_inject = self.monitor.should_inject_knowledge(diversity=0.15, threshold=0.1)
        self.assertFalse(should_inject)

class TestKnowledgeInjector(unittest.TestCase):
    """知识注入模块(AKIM)单元测试"""
    
    def setUp(self):
        """测试前准备"""
        self.injector = KnowledgeInjector()
        
        # 创建测试种群
        self.test_population = [
            {'aircraft_1': [1, 2, 3], 'aircraft_2': [4, 5]},
            {'aircraft_1': [2, 3, 4], 'aircraft_2': [5, 6]}
        ]
        
        self.test_fitness = [
            (100.0, 50.0, 0),  # 添加约束值
            (110.0, 48.0, 0)
        ]
        
        # 创建知识库
        self.knowledge_base = DynamicKnowledgeBase()
        self.knowledge_base.add_solution(
            {'aircraft_1': [1, 3, 5], 'aircraft_2': [2, 4]},
            (95.0, 52.0),
            generation=0
        )
        
    def test_initialization(self):
        """测试初始化"""
        self.assertIsInstance(self.injector.min_injection_ratio, float)
        self.assertIsInstance(self.injector.max_injection_ratio, float)
        
    def test_inject_knowledge_simple(self):
        """测试简化知识注入"""
        original_pop_size = len(self.test_population)
        
        new_population, new_fitness = self.injector.inject_knowledge_simple(
            population=self.test_population.copy(),
            fitness_values=self.test_fitness.copy(),
            knowledge_base=self.knowledge_base,
            injection_count=1
        )
        
        # 检查种群大小保持不变
        self.assertEqual(len(new_population), original_pop_size)
        self.assertEqual(len(new_fitness), original_pop_size)

class TestPathSegmentAnalyzer(unittest.TestCase):
    """关键子路径识别(KSPI)单元测试"""
    
    def setUp(self):
        """测试前准备"""
        self.analyzer = PathSegmentAnalyzer(min_length=3, min_frequency=2)
        
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.analyzer.min_length, 3)
        self.assertEqual(self.analyzer.min_frequency, 2)
        
    def test_extract_path_segments(self):
        """测试路径段提取"""
        test_path = [1, 2, 3, 4, 5]
        segments = self.analyzer._extract_segments(test_path, min_length=3)
        
        expected_segments = [(1, 2, 3), (2, 3, 4), (3, 4, 5)]
        self.assertEqual(segments, expected_segments)
        
    def test_segment_frequency_counting(self):
        """测试路径段频率统计"""
        segments = [(1, 2, 3), (2, 3, 4), (1, 2, 3)]
        frequencies = self.analyzer._count_segment_frequencies(segments)
        
        self.assertEqual(frequencies[(1, 2, 3)], 2)
        self.assertEqual(frequencies[(2, 3, 4)], 1)

class TestConfigValidation(unittest.TestCase):
    """配置验证单元测试"""
    
    def test_valid_config(self):
        """测试有效配置"""
        config = get_akid_config('default')
        is_valid, errors = validate_akid_config(config)
        
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)
        
    def test_config_merging(self):
        """测试配置合并"""
        base_config = {
            'dkb_config': {'capacity': 50},
            'akim_config': {'min_ratio': 0.05}
        }
        
        override_config = {
            'dkb_config': {'capacity': 100},
            'new_config': {'value': 123}
        }
        
        merged = merge_akid_configs(base_config, override_config)
        
        self.assertEqual(merged['dkb_config']['capacity'], 100)
        self.assertEqual(merged['akim_config']['min_ratio'], 0.05)
        self.assertEqual(merged['new_config']['value'], 123)

def run_unit_tests():
    """运行纯单元测试"""
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestDynamicKnowledgeBase,
        TestDiversityMonitor,
        TestKnowledgeInjector,
        TestPathSegmentAnalyzer,
        TestConfigValidation
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果
    print("\n" + "="*60)
    print("AKID-NSGA2 纯单元测试结果")
    print("="*60)
    print(f"总测试数: {result.testsRun}")
    print(f"成功数: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败数: {len(result.failures)}")
    print(f"错误数: {len(result.errors)}")
    
    if result.failures:
        print("\n失败详情:")
        for test, traceback in result.failures:
            print(f"  {test}: {traceback}")
            
    if result.errors:
        print("\n错误详情:")
        for test, traceback in result.errors:
            print(f"  {test}: {traceback}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun if result.testsRun > 0 else 0
    print(f"成功率: {success_rate:.2%}")
    
    # 返回成功状态
    return len(result.failures) == 0 and len(result.errors) == 0

def main():
    """主函数 - 为了兼容性"""
    return run_unit_tests()

if __name__ == "__main__":
    run_unit_tests() 