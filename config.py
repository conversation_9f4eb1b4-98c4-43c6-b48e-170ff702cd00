"""
配置模块 - 包含系统配置参数

本模块定义了系统中使用的各种配置参数，包括：
1. 飞机类别参数
2. 路段类型速度配置
3. 算法参数
4. 物理常数
5. AKID-NSGA2增强参数
"""

# 飞机类别参数
AIRCRAFT_PARAMETERS = {
    # Learjet 35A light
    'light': {
        'weight': 8300,
        'fuel_flow_7': 0.024,
        'fuel_flow_30': 0.067,
        'F0': 2 * 15.6 * 1000,  # 转换为N
        'mu': 0.015  # 滚动阻力系数
    },
    # Airbus A320 medium
    'Medium': {
        'weight': 78000,
        'fuel_flow_7': 0.101,
        'fuel_flow_30': 0.291,
        'F0': 2 * 111.2 * 1000,
        'mu': 0.015  # 滚动阻力系数
    },
    # Airbus A333 heavy
    'Heavy': {
        'weight': 230000,
        'fuel_flow_7': 0.228,
        'fuel_flow_30': 0.724,
        'F0': 2 * 287 * 1000,
        'mu': 0.015  # 滚动阻力系数
    }
}

# 路段类别表，不同路段类别的起始、结束和最大速度，速度单位为米每秒（m/s）
SEGMENT_TYPE_SPEEDS = {
    'straight breakaway': (0, 5.14, 15.43),  # 起始速度0，结束速度5.14 m/s
    'straight holding': (5.14, 0, 15.43),  # 起始速度5.14，结束速度0 m/s
    'straight': (5.14, 5.14, 15.43),  # 起始速度和结束速度均为5.14 m/s
    'turning': (5.14, 5.14, 5.14)  # 转弯段，恒定速度
}

# 物理常数
PHYSICS_CONSTANTS = {
    'acceleration': 0.98,  # 加速度 (m/s^2)
    'deceleration': 0.98,  # 减速度 (m/s^2)
    'cruise_speed': 5.14,  # 匀速滑行速度 (m/s)
    'turning_speed': 5.14,  # 转弯速度 (m/s)
    'epsilon': 1e-6  # 数值计算容忍误差
}

# 算法参数
ALGORITHM_PARAMETERS = {
    'population_size': 100,
    'max_generations': 100,
    'crossover_probability': 0.6,
    'mutation_probability': 0.1,
    'random_factor_max': 4.5
}

# 并行计算参数
PARALLEL_CONFIG = {
    'enable_parallel': True,
    'max_workers': None,  # None表示使用所有可用CPU核心
    'chunk_size': 10  # 每个进程处理的个体数量
}

# 权重类别映射
WEIGHT_CLASS_MAPPING = {
    '1': 'light',
    '2': 'Medium', 
    '3': 'Heavy'
}

# AKID-NSGA2增强配置
AKID_CONFIG = {
    # 总开关
    'enable_akid': True,
    
    # 动态知识库(DKB)配置
    'dkb_config': {
        'capacity': 100,                # 知识库容量
        'elite_ratio': 0.3,            # 精英解比例
        'quality_weight': 0.7,         # 质量权重
        'auto_cleanup': True,          # 自动清理低质量解
        'cleanup_threshold': 0.1       # 清理质量阈值
    },
    
    # 多样性与收敛监控模块(DCMM)配置
    'dcmm_config': {
        'window_size': 5,                    # 监控窗口大小
        'stagnation_threshold': 3,           # 停滞检测阈值(代数)
        'diversity_threshold': 50.0,         # 多样性阈值（适应目标函数标准差量级）
        'convergence_threshold': 0.001,      # 收敛阈值
        'enable_trend_analysis': True,       # 启用趋势分析
        'hypervolume_calculation': 'fast'    # 超体积计算方法: 'fast' or 'accurate'
    },
    
    # 自适应知识注入模块(AKIM)配置
    'akim_config': {
        'injection_ratio_range': (0.05, 0.2),    # 注入比例范围
        'min_injection_size': 1,                 # 最小注入数量
        'max_injection_ratio': 0.3,              # 最大注入比例
        'adaptation_learning_rate': 0.1,         # 自适应学习率
        'success_threshold': 0.7,                # 成功率阈值
        'strategy_weights': {                    # 策略权重
            'diversity': 1.0,
            'stagnation': 1.2,
            'boundary': 0.8
        }
    },
    
    # 关键子路径识别配置
    'key_segment_config': {
        'enable_identification': True,      # 启用子路径识别
        'min_length': 3,                   # 最小子路径长度
        'min_frequency': 3,                # 最小出现频率
        'quality_threshold': 0.5,          # 质量阈值
        'similarity_threshold': 0.7,       # 相似度阈值
        'max_segments_per_aircraft': 10    # 每架飞机最大子路径数
    },
    
    # 性能监控配置
    'monitoring_config': {
        'enable_detailed_logging': False,    # 详细日志
        'save_injection_history': True,     # 保存注入历史
        'performance_metrics': [            # 性能指标
            'hypervolume',
            'diversity',
            'convergence_rate',
            'injection_success_rate'
        ],
        'export_statistics': True           # 导出统计信息
    }
}

# 默认AKID配置（用于快速设置）
DEFAULT_AKID_CONFIG = {
    # DKB参数
    'dkb_capacity': 100,
    'elite_ratio': 0.3,
    'quality_weight': 0.7,
    
    # DCMM参数
    'monitor_window': 5,
    'stagnation_threshold': 3,
    'diversity_threshold': 50.0,
    'convergence_threshold': 0.001,
    
    # AKIM参数
    'injection_ratio_range': (0.05, 0.2),
    'min_injection_size': 1,
    'max_injection_ratio': 0.3,
    
    # 关键子路径参数
    'min_segment_length': 3,
    'min_segment_frequency': 3,
    'segment_quality_threshold': 0.5
}

# 实验配置（用于对比实验）
EXPERIMENT_CONFIG = {
    'baseline_nsga2': {
        'enable_akid': False,
        'algorithm_name': 'Standard NSGA-II'
    },
    
    'akid_nsga2_conservative': {
        'enable_akid': True,
        'dkb_capacity': 50,
        'injection_ratio_range': (0.03, 0.1),
        'stagnation_threshold': 5,
        'algorithm_name': 'AKID-NSGA2 (Conservative)'
    },
    
    'akid_nsga2_aggressive': {
        'enable_akid': True,
        'dkb_capacity': 150,
        'injection_ratio_range': (0.1, 0.3),
        'stagnation_threshold': 2,
        'algorithm_name': 'AKID-NSGA2 (Aggressive)'
    }
}

# 输出配置
OUTPUT_CONFIG = {
    'save_results': True,
    'result_formats': ['npz', 'csv', 'json'],
    'plot_generation_interval': 10,
    'save_final_population': True,
    'export_akid_statistics': True
}

def get_akid_config(config_name='default'):
    """
    获取AKID配置
    
    Args:
        config_name: 配置名称 ('default', 'conservative', 'aggressive')
        
    Returns:
        配置字典
    """
    if config_name == 'default':
        return AKID_CONFIG.copy()
    elif config_name == 'conservative':
        # 保守配置：基于AKID_CONFIG，调整关键参数
        config = AKID_CONFIG.copy()
        config['dkb_config']['capacity'] = 50
        config['dcmm_config']['diversity_threshold'] = 30.0
        config['dcmm_config']['stagnation_threshold'] = 5
        config['akim_config']['injection_ratio_range'] = (0.03, 0.1)
        return config
    elif config_name == 'aggressive':
        # 激进配置：基于AKID_CONFIG，调整关键参数
        config = AKID_CONFIG.copy()
        config['dkb_config']['capacity'] = 150
        config['dcmm_config']['diversity_threshold'] = 80.0
        config['dcmm_config']['stagnation_threshold'] = 2
        config['akim_config']['injection_ratio_range'] = (0.1, 0.3)
        return config
    elif config_name == 'full':
        return AKID_CONFIG.copy()
    else:
        raise ValueError(f"Unknown config name: {config_name}")

def validate_akid_config(config):
    """
    验证AKID配置的有效性
    
    Args:
        config: AKID配置字典
        
    Returns:
        (is_valid, error_message)
    """
    # 检查主要配置结构
    required_sections = ['dkb_config', 'dcmm_config', 'akim_config']
    
    for section in required_sections:
        if section not in config:
            return False, f"Missing required section: {section}"
    
    # 检查DKB配置
    dkb_config = config['dkb_config']
    if 'capacity' not in dkb_config or dkb_config['capacity'] <= 0:
        return False, "dkb_config.capacity must be positive"
    
    if 'elite_ratio' not in dkb_config or not 0 < dkb_config['elite_ratio'] < 1:
        return False, "dkb_config.elite_ratio must be between 0 and 1"
    
    # 检查DCMM配置
    dcmm_config = config['dcmm_config']
    if 'stagnation_threshold' not in dcmm_config or dcmm_config['stagnation_threshold'] <= 0:
        return False, "dcmm_config.stagnation_threshold must be positive"
    
    if 'diversity_threshold' not in dcmm_config or not 0 < dcmm_config['diversity_threshold'] < 1:
        return False, "dcmm_config.diversity_threshold must be between 0 and 1"
    
    # 检查AKIM配置
    akim_config = config['akim_config']
    if 'injection_ratio_range' not in akim_config:
        return False, "akim_config.injection_ratio_range is required"
    
    injection_range = akim_config['injection_ratio_range']
    if not (isinstance(injection_range, (list, tuple)) and len(injection_range) == 2):
        return False, "akim_config.injection_ratio_range must be a tuple/list of 2 values"
    
    if not (0 < injection_range[0] < injection_range[1] < 1):
        return False, "injection_ratio_range must be (low, high) with 0 < low < high < 1"
    
    return True, "Configuration is valid"
