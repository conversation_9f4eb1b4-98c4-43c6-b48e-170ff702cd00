"""
关键子路径识别模块(KSPI) - 关键子路径识别与利用

本模块实现AKID-NSGA2算法的关键子路径识别功能：
1. 关键子路径识别(KSPI)：从知识库中识别高频优质路径段
2. 路径段质量评估和相似性分析
3. 子路径增强和应用策略
4. 路径段统计和管理

主要功能：
- 从优秀解中提取关键子路径
- 基于频率和质量的路径段评估
- 路径段相似性分析和聚类
- 子路径增强策略的应用
"""

import json
import numpy as np
import random
import copy
from typing import List, Tuple, Dict, Any, Optional, Set
from collections import Counter, defaultdict
import networkx as nx


class PathSegmentAnalyzer:
    """
    关键子路径识别：识别和利用高频路径段
    
    主要功能：
    - 路径段提取和分析
    - 基于频率和质量的评估
    - 相似路径段聚类
    - 路径增强策略应用
    """
    
    def __init__(self, min_length=3, min_frequency=2, quality_threshold=0.5, 
                 similarity_threshold=0.7, max_segments_per_aircraft=10):
        """
        初始化路径段分析器
        
        参数:
            min_length: 最小路径段长度
            min_frequency: 最小出现频率
            quality_threshold: 质量阈值
            similarity_threshold: 相似度阈值
            max_segments_per_aircraft: 每架飞机最大路径段数
        """
        self.min_length = min_length
        self.min_frequency = min_frequency
        self.quality_threshold = quality_threshold
        self.similarity_threshold = similarity_threshold
        self.max_segments_per_aircraft = max_segments_per_aircraft
        
        # 路径段存储
        self.key_segments = {}  # {aircraft_type: [segments]}
        self.segment_statistics = {}  # 路径段统计信息
        self.segment_quality_cache = {}  # 质量评估缓存
        
        # 应用历史
        self.application_history = []
        self.enhancement_success_rate = 0.0
        
    def extract_key_segments(self, knowledge_base, aircraft_df=None) -> Dict[str, List[Tuple]]:
        """
        从知识库提取关键子路径
        
        参数:
            knowledge_base: 动态知识库
            aircraft_df: 飞机数据（用于分类分析）
            
        返回:
            关键路径段字典：{aircraft_type: [(segment, frequency, avg_quality)]}
        """
        if not knowledge_base.solutions:
            return {}
            
        # 按飞机类型分组分析
        aircraft_segments = defaultdict(list)
        
        # 提取所有路径段
        for individual, objectives, generation, quality_score in knowledge_base.solutions:
            for aircraft_id, path_encoding in individual.items():
                # 获取飞机类型
                aircraft_type = self._get_aircraft_type(aircraft_id, aircraft_df)
                
                # 提取路径节点序列
                path_nodes = self._extract_path_nodes(path_encoding, aircraft_id)
                
                if len(path_nodes) >= self.min_length:
                    # 提取所有可能的子路径
                    segments = self._extract_segments_from_path(path_nodes, quality_score)
                    aircraft_segments[aircraft_type].extend(segments)
                    
        # 分析每种飞机类型的关键路径段
        key_segments_by_type = {}
        for aircraft_type, segments in aircraft_segments.items():
            key_segments = self._analyze_segments(segments, aircraft_type)
            if key_segments:
                key_segments_by_type[aircraft_type] = key_segments
                
        self.key_segments = key_segments_by_type
        return key_segments_by_type
        
    def _get_aircraft_type(self, aircraft_id: str, aircraft_df=None) -> str:
        """
        获取飞机类型
        
        如果没有飞机数据，使用通用类型
        """
        if aircraft_df is not None:
            try:
                aircraft_row = aircraft_df.loc[aircraft_df['Aircraft ID'] == aircraft_id]
                if not aircraft_row.empty:
                    return aircraft_row['Weight Class'].iloc[0]
            except:
                pass
        return "general"  # 默认类型
        
    def _extract_path_nodes(self, path_encoding: List, aircraft_id: str) -> List[str]:
        """
        从路径编码中提取节点序列
        
        参数:
            path_encoding: 路径编码 [start_time, (M1, M2), (M1, M2), ...]
            aircraft_id: 飞机ID
            
        返回:
            节点序列
        """
        # 这里需要根据实际的路径解码逻辑来实现
        # 由于原项目使用复杂的编码方式，这里简化处理
        # 实际实现时需要调用Decoding模块的相关函数
        
        # 模拟节点序列提取（实际需要根据具体编码逻辑实现）
        if len(path_encoding) <= 1:
            return []
            
        # 假设能够从路径编码中提取出节点序列
        # 实际实现时需要集成解码逻辑
        node_sequence = []
        
        try:
            # 这里应该调用实际的路径解码函数
            # 由于需要图结构等信息，暂时使用模拟数据
            
            # 基于编码长度生成模拟节点序列
            num_nodes = len(path_encoding) - 1  # 减去开始时间
            for i in range(num_nodes):
                node_sequence.append(f"node_{aircraft_id}_{i}")
                
        except Exception as e:
            # 解码失败时返回空序列
            pass
            
        return node_sequence
        
    def _extract_segments_from_path(self, path_nodes: List[str], 
                                  quality_score: float) -> List[Tuple]:
        """
        从路径中提取所有可能的子路径段
        
        参数:
            path_nodes: 节点序列
            quality_score: 路径质量评分
            
        返回:
            路径段列表：[(segment_tuple, quality_score)]
        """
        segments = []
        
        # 提取不同长度的子路径
        for length in range(self.min_length, min(len(path_nodes) + 1, self.min_length + 3)):
            for start_idx in range(len(path_nodes) - length + 1):
                segment = tuple(path_nodes[start_idx:start_idx + length])
                segments.append((segment, quality_score))
                
        return segments
        
    def _analyze_segments(self, segments: List[Tuple], aircraft_type: str) -> List[Tuple]:
        """
        分析路径段，提取关键路径段
        
        参数:
            segments: 路径段列表 [(segment, quality_score), ...]
            aircraft_type: 飞机类型
            
        返回:
            关键路径段：[(segment, frequency, avg_quality, quality_variance)]
        """
        # 统计路径段频率和质量
        segment_stats = defaultdict(list)
        
        for segment, quality in segments:
            segment_stats[segment].append(quality)
            
        # 筛选关键路径段
        key_segments = []
        
        for segment, qualities in segment_stats.items():
            frequency = len(qualities)
            avg_quality = np.mean(qualities)
            quality_variance = np.var(qualities)
            
            # 筛选条件：频率 >= 最小频率，质量 >= 质量阈值
            if (frequency >= self.min_frequency and 
                avg_quality >= self.quality_threshold):
                
                key_segments.append((segment, frequency, avg_quality, quality_variance))
                
        # 按综合评分排序（频率和质量的加权组合）
        key_segments.sort(key=lambda x: x[1] * 0.4 + x[2] * 0.6, reverse=True)
        
        # 限制数量
        key_segments = key_segments[:self.max_segments_per_aircraft]
        
        # 更新统计信息
        self.segment_statistics[aircraft_type] = {
            'total_segments': len(segment_stats),
            'key_segments': len(key_segments),
            'avg_frequency': np.mean([len(q) for q in segment_stats.values()]),
            'avg_quality': np.mean([np.mean(q) for q in segment_stats.values()])
        }
        
        return key_segments
        
    def apply_segment_enhancement(self, population: List[Dict], aircraft_df=None, 
                                prob: float = 0.2, strategy: str = "adaptive") -> Tuple[List[Dict], Dict]:
        """
        应用关键子路径增强
        
        参数:
            population: 当前种群
            aircraft_df: 飞机数据
            prob: 增强概率
            strategy: 增强策略 ("random", "quality_based", "adaptive")
            
        返回:
            (增强后的种群, 增强统计信息)
        """
        if not self.key_segments:
            return population, {
                'enhanced_individuals': 0,
                'total_enhancements': 0,
                'strategy_used': strategy,
                'success': False
            }
            
        enhanced_population = copy.deepcopy(population)
        enhancement_count = 0
        total_enhancements = 0
        
        for i, individual in enumerate(enhanced_population):
            if random.random() < prob:
                enhanced, enhancements = self._enhance_individual(
                    individual, aircraft_df, strategy)
                enhanced_population[i] = enhanced
                if enhancements > 0:
                    enhancement_count += 1
                    total_enhancements += enhancements
                    
        # 记录应用历史
        self._record_enhancement_application(
            enhancement_count, total_enhancements, strategy)
        
        return enhanced_population, {
            'enhanced_individuals': enhancement_count,
            'total_enhancements': total_enhancements,
            'enhancement_rate': enhancement_count / len(population) if population else 0,
            'strategy_used': strategy,
            'success': enhancement_count > 0
        }
        
    def _enhance_individual(self, individual: Dict, aircraft_df=None, 
                          strategy: str = "adaptive") -> Tuple[Dict, int]:
        """
        增强单个个体
        
        参数:
            individual: 个体
            aircraft_df: 飞机数据
            strategy: 增强策略
            
        返回:
            (增强后的个体, 增强次数)
        """
        enhanced_individual = copy.deepcopy(individual)
        enhancement_count = 0
        
        for aircraft_id, path_encoding in enhanced_individual.items():
            aircraft_type = self._get_aircraft_type(aircraft_id, aircraft_df)
            
            if aircraft_type in self.key_segments:
                # 选择合适的路径段进行增强
                selected_segment = self._select_segment_for_enhancement(
                    aircraft_type, path_encoding, strategy)
                
                if selected_segment:
                    # 应用路径段增强
                    success = self._apply_segment_to_path(
                        path_encoding, selected_segment, aircraft_id)
                    if success:
                        enhancement_count += 1
                        
        return enhanced_individual, enhancement_count
        
    def _select_segment_for_enhancement(self, aircraft_type: str, 
                                      path_encoding: List, strategy: str) -> Optional[Tuple]:
        """
        选择用于增强的路径段
        
        参数:
            aircraft_type: 飞机类型
            path_encoding: 当前路径编码
            strategy: 选择策略
            
        返回:
            选择的路径段
        """
        if aircraft_type not in self.key_segments:
            return None
            
        key_segments = self.key_segments[aircraft_type]
        
        if not key_segments:
            return None
            
        if strategy == "random":
            return random.choice(key_segments)
        elif strategy == "quality_based":
            # 选择质量最高的路径段
            return max(key_segments, key=lambda x: x[2])  # x[2] is avg_quality
        elif strategy == "adaptive":
            # 基于历史成功率和质量的自适应选择
            return self._adaptive_segment_selection(key_segments)
        else:
            return random.choice(key_segments)
            
    def _adaptive_segment_selection(self, key_segments: List[Tuple]) -> Tuple:
        """
        自适应路径段选择
        
        结合质量、频率和历史成功率
        """
        if not key_segments:
            return None
            
        # 计算综合评分
        scores = []
        for segment, frequency, avg_quality, quality_variance in key_segments:
            # 综合评分：质量 + 频率权重 - 质量方差惩罚
            score = (avg_quality * 0.6 + 
                    min(frequency / 10.0, 1.0) * 0.3 - 
                    quality_variance * 0.1)
            scores.append(score)
            
        # 基于评分的轮盘赌选择
        if max(scores) <= 0:
            return random.choice(key_segments)
            
        # 归一化评分
        min_score = min(scores)
        if min_score < 0:
            scores = [s - min_score for s in scores]
            
        total_score = sum(scores)
        if total_score == 0:
            return random.choice(key_segments)
            
        # 轮盘赌选择
        rand_val = random.uniform(0, total_score)
        cumulative = 0
        
        for i, score in enumerate(scores):
            cumulative += score
            if cumulative >= rand_val:
                return key_segments[i]
                
        return key_segments[-1]  # 备选
        
    def _apply_segment_to_path(self, path_encoding: List, 
                             segment_info: Tuple, aircraft_id: str) -> bool:
        """
        将路径段应用到路径编码中
        
        参数:
            path_encoding: 路径编码
            segment_info: 路径段信息 (segment, frequency, avg_quality, quality_variance)
            aircraft_id: 飞机ID
            
        返回:
            是否成功应用
        """
        segment, frequency, avg_quality, quality_variance = segment_info
        
        try:
            # 简化的路径段应用策略
            # 实际实现需要根据具体的路径编码和解码逻辑
            
            if len(path_encoding) <= 1:
                return False
                
            # 随机选择插入位置（避免影响起始时间）
            if len(path_encoding) > 2:
                insert_pos = random.randint(1, len(path_encoding) - 1)
                
                # 基于路径段质量调整M1值（影响路径选择）
                if insert_pos < len(path_encoding):
                    current_encoding = path_encoding[insert_pos]
                    if isinstance(current_encoding, tuple) and len(current_encoding) == 2:
                        m1, m2 = current_encoding
                        # 根据路径段质量调整M1值
                        quality_factor = avg_quality
                        adjusted_m1 = m1 * (1 + quality_factor * 0.1)
                        path_encoding[insert_pos] = (adjusted_m1, m2)
                        return True
                        
        except Exception as e:
            # 应用失败
            pass
            
        return False
        
    def _record_enhancement_application(self, enhanced_count: int, 
                                      total_enhancements: int, strategy: str):
        """
        记录增强应用历史
        """
        record = {
            'enhanced_individuals': enhanced_count,
            'total_enhancements': total_enhancements,
            'strategy': strategy,
            'success_rate': enhanced_count / max(1, total_enhancements)
        }
        
        self.application_history.append(record)
        
        # 更新成功率
        if len(self.application_history) > 10:
            recent_records = self.application_history[-10:]
            total_attempts = sum(r['total_enhancements'] for r in recent_records)
            successful_attempts = sum(r['enhanced_individuals'] for r in recent_records)
            self.enhancement_success_rate = (successful_attempts / max(1, total_attempts))
        else:
            self.enhancement_success_rate = enhanced_count / max(1, total_enhancements)
            
    def get_segment_diversity(self, aircraft_type: str = None) -> float:
        """
        计算路径段多样性
        
        参数:
            aircraft_type: 飞机类型，None表示所有类型
            
        返回:
            多样性指标
        """
        if not self.key_segments:
            return 0.0
            
        if aircraft_type and aircraft_type in self.key_segments:
            segments = self.key_segments[aircraft_type]
        else:
            # 计算所有类型的多样性
            all_segments = []
            for segments_list in self.key_segments.values():
                all_segments.extend(segments_list)
            segments = all_segments
            
        if not segments:
            return 0.0
            
        # 基于路径段长度和质量的多样性计算
        lengths = [len(seg[0]) for seg in segments]
        qualities = [seg[2] for seg in segments]
        
        length_diversity = np.std(lengths) if len(set(lengths)) > 1 else 0.0
        quality_diversity = np.std(qualities) if len(qualities) > 1 else 0.0
        
        # 综合多样性指标
        diversity = (length_diversity + quality_diversity) / 2.0
        return diversity
        
    def get_segment_coverage(self, aircraft_type: str = None) -> float:
        """
        计算路径段覆盖率
        
        参数:
            aircraft_type: 飞机类型
            
        返回:
            覆盖率（0-1）
        """
        if not self.segment_statistics:
            return 0.0
            
        if aircraft_type and aircraft_type in self.segment_statistics:
            stats = self.segment_statistics[aircraft_type]
            if stats['total_segments'] > 0:
                return stats['key_segments'] / stats['total_segments']
        else:
            # 计算总体覆盖率
            total_segments = sum(s['total_segments'] for s in self.segment_statistics.values())
            key_segments = sum(s['key_segments'] for s in self.segment_statistics.values())
            
            if total_segments > 0:
                return key_segments / total_segments
                
        return 0.0
        
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取路径段分析统计信息
        
        返回:
            统计信息字典
        """
        total_key_segments = sum(len(segments) for segments in self.key_segments.values())
        
        # 按飞机类型统计
        type_stats = {}
        for aircraft_type, segments in self.key_segments.items():
            if segments:
                frequencies = [seg[1] for seg in segments]
                qualities = [seg[2] for seg in segments]
                
                type_stats[aircraft_type] = {
                    'segment_count': len(segments),
                    'avg_frequency': np.mean(frequencies),
                    'avg_quality': np.mean(qualities),
                    'min_quality': np.min(qualities),
                    'max_quality': np.max(qualities)
                }
                
        return {
            'total_aircraft_types': len(self.key_segments),
            'total_key_segments': total_key_segments,
            'enhancement_success_rate': self.enhancement_success_rate,
            'application_history_length': len(self.application_history),
            'type_statistics': type_stats,
            'segment_statistics': self.segment_statistics,
            'average_segment_length': np.mean([len(seg[0]) for segments in self.key_segments.values() 
                                             for seg in segments]) if total_key_segments > 0 else 0
        }
        
    def clear_segments(self, aircraft_type: str = None):
        """
        清空路径段数据
        
        参数:
            aircraft_type: 飞机类型，None表示清空所有
        """
        if aircraft_type:
            if aircraft_type in self.key_segments:
                del self.key_segments[aircraft_type]
            if aircraft_type in self.segment_statistics:
                del self.segment_statistics[aircraft_type]
        else:
            self.key_segments.clear()
            self.segment_statistics.clear()
            self.segment_quality_cache.clear()
            self.application_history.clear()
            self.enhancement_success_rate = 0.0
            
    def save_segments_to_file(self, filepath: str):
        """
        保存路径段到文件
        
        参数:
            filepath: 保存路径
        """
        import json
        import os
        
        data = {
            'key_segments': {k: [(list(seg[0]), seg[1], seg[2], seg[3]) for seg in v] 
                           for k, v in self.key_segments.items()},
            'segment_statistics': self.segment_statistics,
            'enhancement_success_rate': self.enhancement_success_rate,
            'application_history': self.application_history,
            'config': {
                'min_length': self.min_length,
                'min_frequency': self.min_frequency,
                'quality_threshold': self.quality_threshold,
                'similarity_threshold': self.similarity_threshold,
                'max_segments_per_aircraft': self.max_segments_per_aircraft
            }
        }
        
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)
            
    def load_segments_from_file(self, filepath: str):
        """
        从文件加载路径段
        
        参数:
            filepath: 文件路径
        """
        try:
            with open(filepath, 'r') as f:
                data = json.load(f)
                
            self.key_segments = data.get('key_segments', {})
            self.segment_statistics = data.get('segment_statistics', {})
            self.application_history = data.get('application_history', [])
            self.enhancement_success_rate = data.get('enhancement_success_rate', 0.0)
            
        except FileNotFoundError:
            # 文件不存在，使用空的路径段
            self.key_segments = {}
            self.segment_statistics = {}
            
    def _extract_segments(self, path_nodes: List[str], min_length: int = None) -> List[Tuple]:
        """
        从路径节点提取路径段（兼容性方法）
        
        参数:
            path_nodes: 路径节点列表
            min_length: 最小长度
            
        返回:
            路径段列表
        """
        if min_length is None:
            min_length = self.min_length
            
        segments = []
        # 只提取固定长度的路径段
        for start_idx in range(len(path_nodes) - min_length + 1):
            segment = tuple(path_nodes[start_idx:start_idx + min_length])
            segments.append(segment)
                
        return segments
    
    def _count_segment_frequencies(self, segments: List[Tuple]) -> Dict[Tuple, int]:
        """
        统计路径段频率（兼容性方法）
        
        参数:
            segments: 路径段列表
            
        返回:
            频率字典
        """
        from collections import Counter
        return dict(Counter(segments))
    
    def extract_key_segments_simple(self, knowledge_base) -> List[Tuple]:
        """
        简化的关键路径段提取方法（兼容性方法）
        
        参数:
            knowledge_base: 动态知识库
            
        返回:
            关键路径段列表
        """
        segments_dict = self.extract_key_segments(knowledge_base)
        
        # 合并所有类型的路径段
        all_segments = []
        for aircraft_type, segments in segments_dict.items():
            all_segments.extend(segments)
            
        return all_segments 