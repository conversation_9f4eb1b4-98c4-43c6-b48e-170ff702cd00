# 单目标优化预热初始化策略

## 概述

本项目为AGM_NSGA2实现了一个改进的种群初始化策略——**单目标优化预热初始化策略**。该策略通过先分别优化单个目标函数，然后组合最优解来初始化多目标优化算法的种群，从而提高算法的收敛速度和解的质量。

## 核心思想

传统的随机初始化策略虽然简单，但生成的初始种群质量参差不齐，可能导致算法需要更多代数才能收敛到高质量的解。单目标优化预热初始化策略的核心思想是：

1. **单目标预热**：分别针对总滑行时间(g1)和总燃油消耗(g2)进行单目标优化
2. **精英选择**：从每个单目标优化结果中选取最优的N个解作为精英个体
3. **多样性补充**：随机生成剩余个体以保持种群多样性
4. **组合初始化**：将精英个体与随机个体组合形成高质量的初始种群

## 策略优势

- ✅ **提高初始解质量**：精英个体为算法提供高质量的起始点
- ✅ **加速收敛**：减少算法达到良好解所需的代数
- ✅ **保持多样性**：随机个体确保种群的探索能力
- ✅ **平衡目标**：同时考虑两个目标函数的优化
- ✅ **参数可调**：支持灵活配置以适应不同需求

## 实现架构

### 新增函数

#### 1. `single_objective_environmental_selection()`
- **功能**：单目标环境选择
- **位置**：`MARMT_RK_global.py:189-207`
- **作用**：根据指定目标函数对种群进行排序选择

#### 2. `single_objective_optimizer()`
- **功能**：单目标优化器
- **位置**：`MARMT_RK_global.py:210-282`
- **作用**：针对单个目标函数执行进化算法优化

#### 3. `initialize_population_with_warmup()`
- **功能**：预热初始化主函数
- **位置**：`MARMT_RK_global.py:285-409`
- **作用**：执行完整的单目标优化预热初始化流程

### 修改的函数

#### 1. `MARMT_RK_global()`
- **新增参数**：
  - `use_warmup_initialization`：是否使用预热初始化
  - `warmup_config`：预热配置字典
- **功能增强**：支持选择初始化策略

## 使用方法

### 1. 基本使用

```python
from MARMT_RK_global import MARMT_RK_global

# 使用预热初始化
pareto_front, pareto_set = MARMT_RK_global(
    aircraft_df,
    airport_graph,
    nodes_df,
    edges_df,
    speed_profile_df,
    run_index=1,
    output_folder='results/',
    use_warmup_initialization=True,  # 启用预热初始化
    warmup_config={
        'warmup_generations': 20,
        'warmup_population_size': 50,
        'elite_count_per_objective': 30
    }
)
```

### 2. 在main_global.py中使用

修改`main_global.py`中的配置：

```python
# 初始化策略配置
USE_WARMUP_INITIALIZATION = True  # 启用预热初始化

# 预热配置
WARMUP_CONFIG = {
    'warmup_generations': 20,
    'warmup_population_size': 50,
    'elite_count_per_objective': 30
}
```

## 配置参数

### 参数说明

- **warmup_generations**：单目标优化的代数
  - 范围：5-50
  - 推荐：15-30
  - 影响：代数越多，精英个体质量越高，但计算时间越长

- **warmup_population_size**：单目标优化的种群大小
  - 范围：20-150
  - 推荐：30-100
  - 影响：种群越大，搜索能力越强，但计算时间越长

- **elite_count_per_objective**：每个目标选取的精英个体数
  - 范围：主种群大小的10%-45%
  - 推荐：主种群大小的20%-40%
  - 影响：精英个体越多，初始质量越高，但多样性可能降低

### 推荐配置

| 场景 | warmup_generations | warmup_population_size | elite_count_per_objective | 适用情况 |
|------|-------------------|------------------------|--------------------------|----------|
| 快速测试 | 10 | 30 | 15 | 资源有限，快速验证 |
| 标准应用 | 20 | 50 | 30 | 平衡效果和时间 |
| 高质量 | 30 | 80 | 40 | 对结果质量要求高 |
| 最高质量 | 40 | 100 | 45 | 最高质量要求 |

## 性能对比

理论上，使用单目标优化预热初始化策略应该能够：

1. **减少收敛代数**：通过高质量初始解，算法可能在更少的代数内达到满意的解
2. **提高解质量**：精英个体提供的优质基因有助于找到更好的帕累托前沿
3. **稳定性增强**：减少随机性对算法性能的影响

具体的性能提升程度取决于：
- 问题的复杂度
- 预热配置的参数设置
- 主算法的代数设置

## 注意事项

### 1. 计算时间权衡

- 预热阶段会增加总的计算时间
- 但可能通过减少主算法所需代数来补偿
- 建议根据实际需求选择合适的配置

### 2. 参数设置建议

- 对于快速原型开发，使用较小的预热参数
- 对于实际应用，推荐使用标准配置
- 对于研究和对比实验，可以使用高质量配置

### 3. 内存和性能考虑

- 预热种群大小不宜过大，避免内存问题
- 预热代数适中即可，过多代数收益递减
- 精英个体数要平衡质量和多样性

## 文件结构

```
AGM_NSGA2/
├── MARMT_RK_global.py          # 主算法文件（包含预热初始化）
├── main_global.py              # 主程序
├── config.py                   # 系统配置文件
├── CalFitness_globalV2.py      # 适应度计算
├── operationNSGA2.py           # NSGA-II操作
├── Decoding.py                 # 解码函数
├── ES_global.py                # 环境选择
├── README_WARMUP_INITIALIZATION.md  # 本文档
└── [数据文件]
```

---

**简化说明**：本实现专注于核心功能，预热初始化的配置直接在使用时指定，保持代码结构简洁清晰。 