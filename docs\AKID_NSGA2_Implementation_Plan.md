# AKID-NSGA2算法集成实施计划

## 项目概述

基于对现有机场滑行路径优化项目的分析，本方案将AKID-NSGA2算法集成到现有NSGA-II框架中。项目采用分层模块化设计，主要包含主控模块(`main_global.py`)、优化算法模块(`MARMT_RK_global.py`)、适应度评估模块(`CalFitness_globalV2.py`)、NSGA-II操作模块(`operationNSGA2.py`)和环境选择模块(`ES_global.py`)。

## 1. 代码改造的具体步骤

### 阶段1：核心模块开发（第1-2周）

1. **创建AKID核心模块**
   - 创建`akid_core.py`：实现动态知识库(DKB)和多样性监控(DCMM)
   - 创建`knowledge_injection.py`：实现知识注入模块(AKIM)
   - 创建`path_segments.py`：实现关键子路径识别(KSPI)

2. **扩展配置模块**
   - 在`config.py`中添加AKID相关配置参数
   - 创建AKID配置验证函数

### 阶段2：算法集成（第3-4周）

3. **修改主优化算法**
   - 修改`MARMT_RK_global.py`：集成AKID模块
   - 保持现有接口兼容性，添加AKID开关控制

4. **增强环境选择**
   - 修改`ES_global.py`：添加知识提取功能
   - 保持NSGA-II核心选择逻辑不变

### 阶段3：主控集成（第5周）

5. **更新主控程序**
   - 修改`main_global.py`：添加AKID配置选项
   - 添加算法对比实验功能

### 阶段4：测试与优化（第6周）

6. **测试验证**
   - 单元测试：各AKID模块功能
   - 集成测试：完整算法流程
   - 性能对比：AKID vs 标准NSGA-II

## 2. 需要新增的文件和修改的现有文件清单

### 新增文件

| 文件名 | 功能描述 | 模块对应 |
|--------|----------|----------|
| `akid_core.py` | 动态知识库和多样性监控 | DKB + DCMM |
| `knowledge_injection.py` | 知识注入和适应性调控 | AKIM |
| `path_segments.py` | 关键子路径识别与利用 | KSPI |
| `akid_utils.py` | AKID算法工具函数 | 辅助模块 |
| `test_akid.py` | AKID模块单元测试 | 测试模块 |

### 修改的现有文件

| 文件名 | 修改内容 | 影响程度 |
|--------|----------|----------|
| `config.py` | 添加AKID配置参数 | 轻微 |
| `MARMT_RK_global.py` | 集成AKID主流程 | 中等 |
| `ES_global.py` | 添加知识提取功能 | 轻微 |
| `main_global.py` | 添加AKID配置选项 | 轻微 |

### 保持不变的文件

- `CalFitness_globalV2.py`：适应度评估逻辑不变
- `operationNSGA2.py`：交叉变异操作不变
- `Decoding.py`：路径解码逻辑不变
- 数据文件：`doh1.txt`, `doh1_database.csv`

## 3. 各个核心模块的具体实现方案

### 3.1 动态知识库(DKB) - `akid_core.py`

```python
class DynamicKnowledgeBase:
    """动态知识库：存储和管理优秀解"""
    
    def __init__(self, capacity=50):
        self.capacity = capacity
        self.solutions = []  # [(individual, objectives, generation)]
        
    def add_solution(self, individual, objectives, generation):
        """添加解到知识库"""
        self.solutions.append((copy.deepcopy(individual), objectives, generation))
        if len(self.solutions) > self.capacity:
            self._remove_oldest()
    
    def get_elite_solutions(self, count=5):
        """获取精英解"""
        if not self.solutions:
            return []
        # 按目标函数值排序，返回非支配解
        return self._extract_nondominated(count)
```

### 3.2 多样性监控(DCMM) - `akid_core.py`

```python
class DiversityMonitor:
    """多样性监控：监控种群多样性和收敛状态"""
    
    def __init__(self, window_size=5):
        self.window_size = window_size
        self.diversity_history = []
        
    def calculate_diversity(self, population_objectives):
        """计算种群目标空间多样性"""
        objectives_array = np.array(population_objectives)
        return np.mean(np.std(objectives_array, axis=0))
    
    def should_inject_knowledge(self, diversity, threshold=0.1, generation=None):
        """判断是否需要注入知识"""
        self.diversity_history.append(diversity)
        if len(self.diversity_history) > self.window_size:
            self.diversity_history.pop(0)
            
        # 多样性过低或定期注入
        return diversity < threshold or (generation and generation % 20 == 0)
```

### 3.3 知识注入模块(AKIM) - `knowledge_injection.py`

```python
class KnowledgeInjector:
    """知识注入：自适应注入知识库中的优秀解"""
    
    def __init__(self, min_injection=2, max_injection=5):
        self.min_injection = min_injection
        self.max_injection = max_injection
        
    def inject_knowledge(self, population, fitness_values, knowledge_base, 
                        injection_count=None):
        """向种群注入知识"""
        if not knowledge_base.solutions:
            return population, fitness_values
            
        if injection_count is None:
            injection_count = self.min_injection
            
        # 选择知识库中的优秀解
        elite_solutions = knowledge_base.get_elite_solutions(injection_count)
        
        # 替换种群中最差的个体
        combined = list(zip(population, fitness_values))
        combined.sort(key=lambda x: self._calculate_rank(x[1]))
        
        # 注入知识
        for i, (elite_ind, elite_obj, _) in enumerate(elite_solutions):
            if i < len(combined):
                combined[-(i+1)] = (elite_ind, elite_obj + (0,))  # 添加约束值
                
        population, fitness_values = zip(*combined)
        return list(population), list(fitness_values)
```

### 3.4 关键子路径识别(KSPI) - `path_segments.py`

```python
class PathSegmentAnalyzer:
    """关键子路径识别：识别和利用高频路径段"""
    
    def __init__(self, min_length=3, min_frequency=2):
        self.min_length = min_length
        self.min_frequency = min_frequency
        
    def extract_key_segments(self, knowledge_base):
        """从知识库提取关键子路径"""
        from collections import Counter
        
        segment_counter = Counter()
        for individual, _, _ in knowledge_base.solutions:
            for aircraft_id, path_encoding in individual.items():
                path_nodes = self._extract_path_nodes(path_encoding)
                for i in range(len(path_nodes) - self.min_length + 1):
                    segment = tuple(path_nodes[i:i+self.min_length])
                    segment_counter[segment] += 1
                    
        return [seg for seg, freq in segment_counter.items() 
                if freq >= self.min_frequency]
    
    def apply_segment_enhancement(self, population, key_segments, prob=0.2):
        """应用关键子路径增强"""
        for individual in population:
            if random.random() < prob and key_segments:
                segment = random.choice(key_segments)
                # 在个体的路径中插入关键子路径
                self._insert_segment(individual, segment)
```

## 4. 与现有NSGA-II框架的集成方式

### 4.1 主算法流程集成 - 修改`MARMT_RK_global.py`

```python
def MARMT_RK_global_with_akid(aircraft_df, airport_graph, nodes_df, edges_df, 
                             speed_profile_df, run_index, output_folder,
                             use_akid=True, akid_config=None):
    """集成AKID的主优化算法"""
    
    # 初始化AKID模块
    if use_akid:
        knowledge_base = DynamicKnowledgeBase(capacity=akid_config.get('dkb_capacity', 50))
        diversity_monitor = DiversityMonitor()
        knowledge_injector = KnowledgeInjector()
        segment_analyzer = PathSegmentAnalyzer()
    
    # 原有初始化流程保持不变
    # ... 现有代码 ...
    
    for generation in range(max_generations):
        # 原有NSGA-II流程
        offspring_population = crossover_and_mutate(population, ...)
        combined_population = population + offspring_population
        combined_fitness = parent_fitness + offspring_fitness
        
        # 环境选择
        new_population, new_fitness, pareto_front, pareto_set = environmental_selection(
            population, offspring_population, parent_fitness, offspring_fitness, population_size)
        
        if use_akid:
            # AKID增强流程
            # 1. 更新知识库
            for ind, fit in zip(pareto_set, pareto_front):
                knowledge_base.add_solution(ind, fit[:2], generation)
            
            # 2. 多样性监控
            diversity = diversity_monitor.calculate_diversity([f[:2] for f in new_fitness])
            
            # 3. 知识注入决策
            if diversity_monitor.should_inject_knowledge(diversity, generation=generation):
                new_population, new_fitness = knowledge_injector.inject_knowledge(
                    new_population, new_fitness, knowledge_base)
            
            # 4. 关键子路径应用（每20代执行一次）
            if generation % 20 == 0:
                key_segments = segment_analyzer.extract_key_segments(knowledge_base)
                segment_analyzer.apply_segment_enhancement(new_population, key_segments)
        
        # 更新种群
        population = new_population
        parent_fitness = new_fitness
```

### 4.2 环境选择增强 - 修改`ES_global.py`

```python
def environmental_selection_with_knowledge_extraction(parent_population, offspring_population, 
                                                    parent_fitness, offspring_fitness, 
                                                    population_size, knowledge_base=None):
    """增强的环境选择，支持知识提取"""
    
    # 执行原有NSGA-II选择
    new_population, new_fitness, pareto_front, pareto_set = environmental_selection(
        parent_population, offspring_population, parent_fitness, offspring_fitness, population_size)
    
    # 如果启用知识库，提取精英解
    if knowledge_base is not None:
        # 将帕累托前沿解添加到知识库
        for ind, fit in zip(pareto_set, pareto_front):
            if fit[2] == 0:  # 仅添加可行解
                knowledge_base.add_solution(ind, fit[:2], None)
    
    return new_population, new_fitness, pareto_front, pareto_set
```

### 4.3 配置集成 - 修改`config.py`

```python
# 在现有配置基础上添加AKID配置
AKID_NSGA2_CONFIG = {
    'enable_akid': True,
    'dkb_capacity': 50,
    'diversity_threshold': 0.1,
    'injection_frequency': 20,
    'min_injection_count': 2,
    'segment_min_length': 3,
    'segment_min_frequency': 2,
    'segment_enhancement_prob': 0.2
}
```

### 4.4 主控程序集成 - 修改`main_global.py`

```python
def main():
    # 现有配置 + AKID配置
    USE_AKID_ENHANCEMENT = True  # AKID增强开关
    
    AKID_CONFIG = {
        'dkb_capacity': 50,
        'diversity_threshold': 0.1,
        'injection_frequency': 20,
        'min_injection_count': 2
    }
    
    # 在主循环中调用增强算法
    for run_index in range(1, NUM_RUNS + 1):
        if USE_AKID_ENHANCEMENT:
            pareto_front, pareto_set = MARMT_RK_global_with_akid(
                aircraft_df, airport_graph, nodes_df, edges_df, speed_profile_df,
                run_index, output_folder, use_akid=True, akid_config=AKID_CONFIG)
        else:
            pareto_front, pareto_set = MARMT_RK_global(
                aircraft_df, airport_graph, nodes_df, edges_df, speed_profile_df,
                run_index, output_folder)
```

## 5. 实施优势和风险评估

### 5.1 实施优势

1. **模块化设计**：AKID模块独立实现，不影响现有算法逻辑
2. **向后兼容**：保持原有接口，可通过开关控制是否启用AKID
3. **渐进实施**：可分阶段实施，逐步验证功能
4. **最小侵入**：对现有核心模块修改最小

### 5.2 潜在风险

1. **性能开销**：AKID模块可能增加计算时间
2. **参数敏感**：需要调优AKID相关参数
3. **内存消耗**：知识库存储增加内存使用

### 5.3 风险缓解措施

1. **性能监控**：添加时间统计，监控性能影响
2. **参数自适应**：实现参数自动调整机制
3. **内存管理**：实现知识库容量限制和清理机制

## 6. 验证和测试计划

### 6.1 功能测试

1. **单元测试**：每个AKID模块的基本功能
2. **集成测试**：AKID与NSGA-II的集成效果
3. **回归测试**：确保不影响原有功能

### 6.2 性能对比测试

1. **收敛性对比**：AKID-NSGA2 vs 标准NSGA-II
2. **多样性对比**：帕累托前沿分布质量
3. **计算效率对比**：运行时间和内存使用

### 6.3 实验设计

```python
# 对比实验配置
EXPERIMENT_CONFIGS = {
    'standard_nsga2': {'use_akid': False},
    'akid_conservative': {'use_akid': True, 'dkb_capacity': 30, 'injection_frequency': 30},
    'akid_aggressive': {'use_akid': True, 'dkb_capacity': 80, 'injection_frequency': 15}
}
```


## 9. 结论

本实施计划在保持现有系统稳定性的前提下，以最小侵入的方式集成AKID-NSGA2算法。通过模块化设计和渐进式实施，可以有效降低集成风险，同时为算法性能提升提供强有力的技术支撑。

实施完成后，系统将具备：
- 动态知识管理能力
- 自适应多样性调控
- 智能知识注入机制
- 关键路径模式识别

这些增强功能将显著提升机场滑行路径优化的求解质量和算法性能。 