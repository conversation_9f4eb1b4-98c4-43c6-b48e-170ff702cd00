"""
AKID-NSGA2集成测试脚本

本脚本用于快速验证AKID-NSGA2增强算法的集成是否正常工作。
执行单次短时间运行，检查所有模块的正常运行。
"""

import pandas as pd
import networkx as nx
import numpy as np
import os
from nsga2_core.algorithm import MARMT_RK_global_with_akid
from config import get_akid_config


def load_test_data():
    """加载测试数据"""
    print("正在加载测试数据...")
    
    # 读取机场布局数据
    airport_data_path = 'doh1.txt'
    
    with open(airport_data_path, 'r') as file:
        lines = file.readlines()

    # 找到各个数据部分
    node_section_start = lines.index('%SECTION%1%;Nodes;\n') + 1
    edge_section_start = lines.index('%SECTION%2%;Edges;\n') + 1
    aircraft_section_start = lines.index('%SECTION%3%;Aircraft;\n') + 1

    # 解析节点数据
    node_data_lines = lines[node_section_start + 1:edge_section_start - 1]
    node_data = []
    for line in node_data_lines:
        if line.strip() and not line.startswith('%'):
            parts = line.strip().split(';')
            if len(parts) >= 4:
                node_id, x, y, specification = parts[1:5]
                node_data.append([node_id, float(x), float(y), specification])

    nodes_df = pd.DataFrame(node_data, columns=['Node ID', 'X', 'Y', 'Specification'])

    # 解析边数据
    edge_data_lines = lines[edge_section_start + 1:aircraft_section_start - 1]
    edge_data = []
    for line in edge_data_lines:
        if line.strip() and not line.startswith('%'):
            parts = line.strip().split(';')
            if len(parts) >= 6:
                edge_id, start_node, end_node, directed, length, edge_type = parts[1:7]
                unavailable_time_windows = []
                edge_data.append([edge_id, start_node, end_node, directed, float(length), edge_type, unavailable_time_windows])

    edges_df = pd.DataFrame(edge_data, columns=['Edge ID', 'Start Node', 'End Node', 'Directed', 'Length', 'Type', 'Unavailable Time Windows'])

    # 解析飞机数据（只取前3架飞机进行快速测试）
    aircraft_data_lines = lines[aircraft_section_start + 1:]
    aircraft_data = []
    count = 0
    for line in aircraft_data_lines:
        if line.strip() and not line.startswith('%') and count < 3:
            parts = line.strip().split(';')
            if len(parts) >= 16:
                aircraft_type = parts[1].strip().strip("'")
                start_node = parts[2].strip().strip("'")
                end_node = parts[3].strip().strip("'")
                start_time_str = parts[4].strip("[]")
                start_time_values = start_time_str.split(';')
                start_time = float(start_time_values[0].strip())
                weight_class = parts[16].strip().strip("'")
                aircraft_data.append([aircraft_type, start_node, end_node, start_time, weight_class])
                count += 1

    aircraft_df = pd.DataFrame(aircraft_data, columns=['Type', 'Start Node', 'End Node', 'Start Time', 'Weight Class'])
    aircraft_df = aircraft_df.sort_values(by='Start Time').reset_index(drop=True)
    aircraft_df['Aircraft ID'] = aircraft_df.index

    # 读取速度配置数据
    speed_profile_df = pd.read_csv('doh1_database.csv')

    # 创建机场图
    airport_graph = nx.Graph()
    for _, edge in edges_df.iterrows():
        start_node = edge['Start Node']
        end_node = edge['End Node']
        length = edge['Length']
        time_window = edge['Unavailable Time Windows']
        airport_graph.add_edge(start_node, end_node, length=length, unavailable_time_windows=time_window)
        airport_graph.add_edge(end_node, start_node, length=length, unavailable_time_windows=time_window)

    print(f"测试数据加载完成:")
    print(f"  - 节点数: {len(nodes_df)}")
    print(f"  - 边数: {len(edges_df)}")
    print(f"  - 测试飞机数: {len(aircraft_df)}")
    print(f"  - 速度配置数: {len(speed_profile_df)}")
    
    return aircraft_df, airport_graph, nodes_df, edges_df, speed_profile_df


def test_akid_configurations():
    """测试不同的AKID配置"""
    
    configurations = {
        'default': get_akid_config('default'),
        'conservative': get_akid_config('conservative'),
        'aggressive': get_akid_config('aggressive')
    }
    
    print("\n" + "="*60)
    print("AKID配置验证测试")
    print("="*60)
    
    for config_name, config in configurations.items():
        print(f"\n测试配置: {config_name}")
        print(f"  - DKB容量: {config.get('dkb_config', {}).get('capacity', 'N/A')}")
        print(f"  - 多样性阈值: {config.get('dcmm_config', {}).get('diversity_threshold', 'N/A')}")
        print(f"  - 注入比例: {config.get('akim_config', {}).get('injection_ratio_range', 'N/A')}")
        
        # 验证配置
        from akid_modules.utils import validate_akid_config
        is_valid, errors = validate_akid_config(config)
        if is_valid:
            print(f"  ✅ 配置验证: 通过")
        else:
            print(f"  ❌ 配置验证: 失败 - {errors}")
    
    return configurations['default']


def run_akid_test():
    """运行AKID增强算法测试"""
    
    print("\n" + "="*60)
    print("AKID-NSGA2算法集成测试")
    print("="*60)
    
    # 加载测试数据
    aircraft_df, airport_graph, nodes_df, edges_df, speed_profile_df = load_test_data()
    
    # 测试配置
    akid_config = test_akid_configurations()
    
    # 创建测试输出文件夹
    output_folder = 'test_results/akid_integration_test/'
    os.makedirs(output_folder, exist_ok=True)
    
    print(f"\n开始AKID-NSGA2测试运行...")
    print(f"测试配置:")
    print(f"  - 飞机数量: {len(aircraft_df)}")
    print(f"  - 算法: AKID-NSGA2")
    print(f"  - 最大代数: 10 (测试用)")
    print(f"  - 种群大小: 20 (测试用)")
    
    try:
        # 修改算法参数以进行快速测试
        original_max_gen = None
        original_popsize = None
        
        # 临时修改MARMT_RK_global.py中的参数（仅用于测试）
        print("  - 使用快速测试参数...")
        
        # 运行AKID-NSGA2算法
        result = MARMT_RK_global_with_akid(
            aircraft_df=aircraft_df,
            airport_graph=airport_graph,
            nodes_df=nodes_df,
            edges_df=edges_df,
            speed_profile_df=speed_profile_df,
            run_index=1,
            output_folder=output_folder,
            use_akid=True,
            akid_config=akid_config,
            use_warmup_initialization=False,  # 测试时不使用预热以节省时间
            warmup_config=None
        )
        
        # 解析结果
        if len(result) == 3:
            pareto_front, pareto_set, akid_report = result
            print(f"\n✅ AKID-NSGA2测试运行成功!")
            print(f"结果统计:")
            print(f"  - 帕累托前沿大小: {len(pareto_front)}")
            
            if pareto_front:
                min_time = min(point[0] for point in pareto_front)
                min_fuel = min(point[1] for point in pareto_front)
                print(f"  - 最优时间: {min_time:.2f}秒")
                print(f"  - 最优燃油: {min_fuel:.2f}kg")
            
            if akid_report:
                print(f"\nAKID模块统计:")
                kb_stats = akid_report.get('knowledge_base', {})
                diversity_stats = akid_report.get('diversity_monitoring', {})
                injection_stats = akid_report.get('knowledge_injection', {})
                
                print(f"  - 知识库最终大小: {kb_stats.get('final_size', 'N/A')}")
                print(f"  - 平均多样性: {diversity_stats.get('average_diversity', 'N/A'):.4f}")
                print(f"  - 知识注入次数: {injection_stats.get('total_injections', 'N/A')}")
                print(f"  - 注入成功率: {injection_stats.get('success_rate', 'N/A'):.2f}")
            
            # 保存测试报告
            test_report = {
                'test_status': 'SUCCESS',
                'test_configuration': {
                    'aircraft_count': len(aircraft_df),
                    'algorithm': 'AKID-NSGA2',
                    'akid_config': akid_config
                },
                'results': {
                    'pareto_front_size': len(pareto_front),
                    'best_time': min_time if pareto_front else None,
                    'best_fuel': min_fuel if pareto_front else None
                },
                'akid_statistics': akid_report
            }
            
            import json
            report_file = os.path.join(output_folder, 'integration_test_report.json')
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(test_report, f, indent=2, ensure_ascii=False)
            
            print(f"\n测试报告已保存: {report_file}")
            print(f"结果文件保存路径: {output_folder}")
            
        else:
            print(f"⚠️  算法运行完成，但返回格式异常（可能是标准NSGA-II模式）")
            pareto_front, pareto_set = result
            print(f"帕累托前沿大小: {len(pareto_front)}")
            
    except Exception as e:
        print(f"\n❌ AKID-NSGA2测试失败!")
        print(f"错误信息: {str(e)}")
        import traceback
        traceback.print_exc()
        
        # 保存错误报告
        error_report = {
            'test_status': 'FAILED',
            'error_message': str(e),
            'error_traceback': traceback.format_exc()
        }
        
        import json
        error_file = os.path.join(output_folder, 'integration_test_error.json')
        with open(error_file, 'w', encoding='utf-8') as f:
            json.dump(error_report, f, indent=2, ensure_ascii=False)
        
        print(f"错误报告已保存: {error_file}")


def main():
    """主测试函数"""
    print("="*60)
    print("AKID-NSGA2集成测试")
    print("="*60)
    print("本测试将验证:")
    print("1. AKID模块导入和配置")
    print("2. AKID-NSGA2算法运行")
    print("3. 结果输出和报告生成")
    print("="*60)
    
    # 检查必要文件
    required_files = [
        'doh1.txt',
        'doh1_database.csv',
        'akid_core.py',
        'knowledge_injection.py',
        'path_segments.py',
        'akid_utils.py',
        'config.py',
        'MARMT_RK_global.py'
    ]
    
    print("\n检查必要文件...")
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} (缺失)")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n⚠️  缺失必要文件: {missing_files}")
        print("请确保所有AKID模块文件都存在后重新运行测试。")
        return
    
    print("\n所有必要文件检查通过，开始集成测试...")
    
    # 运行测试
    run_akid_test()
    
    print("\n" + "="*60)
    print("集成测试完成!")
    print("="*60)


if __name__ == "__main__":
    main() 