"""
AKID-NSGA2实验设计配置

实现验证和测试计划第6.3节：实验设计
按照AKID_NSGA2_Implementation_Plan.md第6节要求实施对比实验配置

包含：
- 标准NSGA-II基准测试
- AKID-NSGA2保守配置测试  
- AKID-NSGA2激进配置测试
- 多种实验场景和参数配置
"""

import os
import json
import time
from typing import Dict, List, Any
from config import get_akid_config

# 按照实施计划要求的实验配置
EXPERIMENT_CONFIGS = {
    'standard_nsga2': {
        'algorithm_name': 'Standard NSGA-II',
        'use_akid': False,
        'description': '标准NSGA-II算法基准测试',
        'expected_performance': 'baseline',
        'test_parameters': {
            'population_size': 100,
            'max_generations': 100,
            'crossover_probability': 0.6,
            'mutation_probability': 0.1
        }
    },
    
    'akid_conservative': {
        'algorithm_name': 'AKID-NSGA2 (Conservative)',
        'use_akid': True,
        'akid_config_name': 'conservative',
        'description': 'AKID-NSGA2保守配置：强调稳定性和可靠性',
        'expected_performance': 'stable_improvement',
        'akid_parameters': {
            'dkb_capacity': 50,
            'diversity_threshold': 0.2,
            'injection_frequency': 30,
            'injection_ratio_range': (0.03, 0.1),
            'stagnation_threshold': 5
        }
    },
    
    'akid_aggressive': {
        'algorithm_name': 'AKID-NSGA2 (Aggressive)',
        'use_akid': True,
        'akid_config_name': 'aggressive',
        'description': 'AKID-NSGA2激进配置：追求快速收敛和高性能',
        'expected_performance': 'rapid_convergence',
        'akid_parameters': {
            'dkb_capacity': 150,
            'diversity_threshold': 0.4,
            'injection_frequency': 15,
            'injection_ratio_range': (0.1, 0.3),
            'stagnation_threshold': 2
        }
    },
    
    'akid_default': {
        'algorithm_name': 'AKID-NSGA2 (Default)',
        'use_akid': True,
        'akid_config_name': 'default',
        'description': 'AKID-NSGA2默认配置：平衡性能和稳定性',
        'expected_performance': 'balanced_improvement',
        'akid_parameters': {
            'dkb_capacity': 100,
            'diversity_threshold': 0.3,
            'injection_frequency': 20,
            'injection_ratio_range': (0.05, 0.2),
            'stagnation_threshold': 3
        }
    }
}

# 多场景测试配置
SCENARIO_CONFIGS = {
    'small_scale': {
        'name': '小规模场景',
        'max_aircraft': 5,
        'max_generations': 50,
        'population_size': 50,
        'expected_runtime': '1-2分钟',
        'focus': '基本功能验证'
    },
    
    'medium_scale': {
        'name': '中等规模场景',
        'max_aircraft': 10,
        'max_generations': 100,
        'population_size': 100,
        'expected_runtime': '5-10分钟',
        'focus': '性能对比测试'
    },
    
    'large_scale': {
        'name': '大规模场景',
        'max_aircraft': 20,
        'max_generations': 200,
        'population_size': 150,
        'expected_runtime': '15-30分钟',
        'focus': '算法可扩展性测试'
    },
    
    'stress_test': {
        'name': '压力测试场景',
        'max_aircraft': 30,
        'max_generations': 300,
        'population_size': 200,
        'expected_runtime': '30-60分钟',
        'focus': '算法稳定性和极限性能'
    }
}

# 评估指标定义
EVALUATION_METRICS = {
    'primary_metrics': {
        'hypervolume': {
            'description': '超体积指标',
            'objective': 'maximize',
            'weight': 0.4,
            'calculation': 'HV计算',
            'interpretation': '值越大表示解集质量越好'
        },
        'convergence_rate': {
            'description': '收敛速度',
            'objective': 'maximize',
            'weight': 0.3,
            'calculation': '达到收敛阈值的代数',
            'interpretation': '收敛越快表示算法效率越高'
        },
        'solution_diversity': {
            'description': '解的多样性',
            'objective': 'maximize',
            'weight': 0.3,
            'calculation': '帕累托前沿分布均匀性',
            'interpretation': '多样性越高表示解集覆盖范围越广'
        }
    },
    
    'secondary_metrics': {
        'execution_time': {
            'description': '执行时间',
            'objective': 'minimize',
            'unit': 'seconds',
            'interpretation': '时间越短表示算法效率越高'
        },
        'memory_usage': {
            'description': '内存使用量',
            'objective': 'minimize',
            'unit': 'MB',
            'interpretation': '内存使用越少表示算法开销越小'
        },
        'pareto_front_size': {
            'description': '帕累托前沿大小',
            'objective': 'maximize',
            'unit': 'count',
            'interpretation': '前沿点数越多表示解的丰富度越高'
        }
    },
    
    'akid_specific_metrics': {
        'knowledge_injection_count': {
            'description': 'AKID知识注入次数',
            'unit': 'count',
            'interpretation': '注入次数反映AKID模块的活跃度'
        },
        'knowledge_base_utilization': {
            'description': '知识库利用率',
            'unit': 'percentage',
            'calculation': '实际使用容量/总容量',
            'interpretation': '利用率反映知识库效率'
        },
        'diversity_improvement': {
            'description': '多样性改善程度',
            'calculation': 'AKID前后多样性对比',
            'interpretation': '改善程度反映AKID效果'
        }
    }
}

# 统计分析配置
STATISTICAL_ANALYSIS_CONFIG = {
    'significance_level': 0.05,
    'confidence_interval': 0.95,
    'test_methods': {
        'normality_test': 'Shapiro-Wilk',
        'variance_test': 'Levene',
        'mean_comparison': 'Welch t-test',
        'non_parametric_comparison': 'Mann-Whitney U'
    },
    'effect_size_metrics': ['Cohen_d', 'eta_squared'],
    'multiple_comparison_correction': 'Bonferroni'
}


class ExperimentalDesigner:
    """实验设计管理器"""
    
    def __init__(self):
        self.experiment_configs = EXPERIMENT_CONFIGS
        self.scenario_configs = SCENARIO_CONFIGS
        self.evaluation_metrics = EVALUATION_METRICS
        self.statistical_config = STATISTICAL_ANALYSIS_CONFIG
        
    def generate_full_experimental_plan(self) -> Dict[str, Any]:
        """生成完整的实验计划"""
        
        experimental_plan = {
            'plan_metadata': {
                'creation_date': time.strftime('%Y-%m-%d %H:%M:%S'),
                'version': '1.0',
                'description': 'AKID-NSGA2完整实验验证计划',
                'based_on': 'AKID_NSGA2_Implementation_Plan.md 第6节'
            },
            
            'experimental_matrix': self._create_experimental_matrix(),
            'evaluation_framework': self.evaluation_metrics,
            'statistical_analysis': self.statistical_config,
            'expected_outcomes': self._define_expected_outcomes(),
            'risk_assessment': self._assess_experimental_risks(),
            'resource_requirements': self._estimate_resource_requirements()
        }
        
        return experimental_plan
    
    def _create_experimental_matrix(self) -> Dict[str, Any]:
        """创建实验矩阵"""
        
        matrix = {
            'algorithms': list(self.experiment_configs.keys()),
            'scenarios': list(self.scenario_configs.keys()),
            'replications': 30,  # 每个配置运行30次
            'total_experiments': len(self.experiment_configs) * len(self.scenario_configs) * 30,
            'estimated_duration': '4-6小时',
            
            'experiment_combinations': []
        }
        
        # 生成所有实验组合
        experiment_id = 1
        for scenario_name, scenario_config in self.scenario_configs.items():
            for alg_name, alg_config in self.experiment_configs.items():
                combination = {
                    'experiment_id': experiment_id,
                    'algorithm': alg_name,
                    'scenario': scenario_name,
                    'replications': matrix['replications'],
                    'priority': self._assign_priority(alg_name, scenario_name),
                    'expected_runtime': self._estimate_combination_runtime(scenario_config, matrix['replications'])
                }
                matrix['experiment_combinations'].append(combination)
                experiment_id += 1
        
        return matrix
    
    def _assign_priority(self, algorithm: str, scenario: str) -> str:
        """分配实验优先级"""
        # 核心对比实验优先级最高
        if algorithm in ['standard_nsga2', 'akid_default'] and scenario == 'medium_scale':
            return 'HIGH'
        # 扩展配置测试优先级中等
        elif algorithm in ['akid_conservative', 'akid_aggressive'] and scenario in ['small_scale', 'medium_scale']:
            return 'MEDIUM'
        # 压力测试优先级较低
        else:
            return 'LOW'
    
    def _estimate_combination_runtime(self, scenario_config: Dict, replications: int) -> str:
        """估算实验组合运行时间"""
        base_times = {
            'small_scale': 2,      # 2分钟
            'medium_scale': 8,     # 8分钟
            'large_scale': 25,     # 25分钟
            'stress_test': 45      # 45分钟
        }
        
        aircraft_count = scenario_config['max_aircraft']
        if aircraft_count <= 5:
            base_time = base_times['small_scale']
        elif aircraft_count <= 10:
            base_time = base_times['medium_scale']
        elif aircraft_count <= 20:
            base_time = base_times['large_scale']
        else:
            base_time = base_times['stress_test']
        
        total_minutes = base_time * replications
        hours = total_minutes // 60
        minutes = total_minutes % 60
        
        if hours > 0:
            return f"{hours}小时{minutes}分钟"
        else:
            return f"{minutes}分钟"
    
    def _define_expected_outcomes(self) -> Dict[str, Any]:
        """定义预期结果"""
        
        return {
            'hypotheses': {
                'H1': {
                    'description': 'AKID-NSGA2在超体积指标上显著优于标准NSGA-II',
                    'type': 'superiority',
                    'effect_size': 'medium_to_large',
                    'significance_level': 0.05
                },
                'H2': {
                    'description': 'AKID-NSGA2在收敛速度上显著快于标准NSGA-II',
                    'type': 'superiority',
                    'effect_size': 'medium',
                    'significance_level': 0.05
                },
                'H3': {
                    'description': 'AKID激进配置在快速收敛场景下表现最佳',
                    'type': 'conditional_superiority',
                    'conditions': ['high_diversity_threshold', 'frequent_injection'],
                    'significance_level': 0.05
                },
                'H4': {
                    'description': 'AKID保守配置在稳定性要求场景下表现最佳',
                    'type': 'conditional_superiority',
                    'conditions': ['low_diversity_threshold', 'infrequent_injection'],
                    'significance_level': 0.05
                }
            },
            
            'performance_expectations': {
                'standard_nsga2': {
                    'hypervolume': 'baseline',
                    'convergence_rate': 'baseline',
                    'stability': 'high',
                    'memory_usage': 'low'
                },
                'akid_default': {
                    'hypervolume': '10-20% improvement',
                    'convergence_rate': '15-25% faster',
                    'stability': 'high',
                    'memory_usage': '5-10% increase'
                },
                'akid_conservative': {
                    'hypervolume': '5-15% improvement',
                    'convergence_rate': '10-20% faster',
                    'stability': 'very high',
                    'memory_usage': '3-7% increase'
                },
                'akid_aggressive': {
                    'hypervolume': '15-30% improvement',
                    'convergence_rate': '20-35% faster',
                    'stability': 'medium to high',
                    'memory_usage': '10-15% increase'
                }
            }
        }
    
    def _assess_experimental_risks(self) -> Dict[str, Any]:
        """评估实验风险"""
        
        return {
            'technical_risks': {
                'algorithm_failure': {
                    'probability': 'low',
                    'impact': 'medium',
                    'mitigation': '增加错误处理和回退机制'
                },
                'performance_degradation': {
                    'probability': 'medium',
                    'impact': 'low',
                    'mitigation': '设置性能基准和监控'
                },
                'memory_overflow': {
                    'probability': 'low',
                    'impact': 'high',
                    'mitigation': '限制知识库大小和监控内存使用'
                }
            },
            
            'methodological_risks': {
                'insufficient_replications': {
                    'probability': 'low',
                    'impact': 'medium',
                    'mitigation': '确保每个配置至少30次重复'
                },
                'biased_test_data': {
                    'probability': 'medium',
                    'impact': 'high',
                    'mitigation': '使用多种规模和复杂度的测试场景'
                },
                'statistical_power': {
                    'probability': 'medium',
                    'impact': 'medium',
                    'mitigation': '进行功效分析和样本量计算'
                }
            },
            
            'external_risks': {
                'computational_resources': {
                    'probability': 'medium',
                    'impact': 'medium',
                    'mitigation': '分批执行和云计算资源备份'
                },
                'time_constraints': {
                    'probability': 'medium',
                    'impact': 'low',
                    'mitigation': '按优先级执行核心实验'
                }
            }
        }
    
    def _estimate_resource_requirements(self) -> Dict[str, Any]:
        """估算资源需求"""
        
        return {
            'computational_resources': {
                'cpu_cores': '8-16核心',
                'memory': '16-32GB RAM',
                'storage': '50-100GB',
                'estimated_cost': 'cloud: $50-100'
            },
            
            'time_requirements': {
                'development': '已完成',
                'testing_setup': '2-4小时',
                'experiment_execution': '24-48小时',
                'data_analysis': '8-16小时',
                'report_generation': '4-8小时',
                'total_timeline': '3-5天'
            },
            
            'human_resources': {
                'experiment_execution': '1人，监控和维护',
                'data_analysis': '1人，统计分析专长',
                'result_interpretation': '1-2人，算法和应用专长'
            }
        }
    
    def create_experiment_script(self) -> str:
        """创建实验执行脚本"""
        
        script = '''#!/usr/bin/env python3
"""
AKID-NSGA2 实验执行脚本
自动执行完整的实验计划
"""

import os
import json
import time
from tests_suite.test_akid_unit_tests import run_akid_unit_tests
from tests_suite.test_akid_performance_comparison import run_performance_comparison_test
from tests_suite.test_akid_integration import main as run_integration_test

def main():
    print("="*80)
    print("AKID-NSGA2 完整实验验证")
    print("="*80)
    print("按照实施计划第6节要求执行完整验证...")
    print()
    
    results = {}
    
    # 阶段1：单元测试
    print("阶段1：执行单元测试...")
    unit_test_success, unit_test_report = run_akid_unit_tests()
    results['unit_tests'] = {
        'success': unit_test_success,
        'report': unit_test_report
    }
    
    # 阶段2：集成测试
    print("\\n阶段2：执行集成测试...")
    try:
        run_integration_test()
        results['integration_tests'] = {'success': True}
    except Exception as e:
        results['integration_tests'] = {'success': False, 'error': str(e)}
    
    # 阶段3：性能对比测试
    print("\\n阶段3：执行性能对比测试...")
    perf_test_success = run_performance_comparison_test()
    results['performance_tests'] = {'success': perf_test_success}
    
    # 生成最终报告
    final_report = {
        'experiment_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'overall_success': all([
            results['unit_tests']['success'],
            results['integration_tests']['success'],
            results['performance_tests']['success']
        ]),
        'stage_results': results
    }
    
    # 保存最终报告
    os.makedirs('test_results/final_report/', exist_ok=True)
    with open('test_results/final_report/complete_validation_report.json', 'w') as f:
        json.dump(final_report, f, indent=2)
    
    # 输出总结
    print("\\n" + "="*80)
    print("实验验证总结")
    print("="*80)
    
    if final_report['overall_success']:
        print("✅ 所有验证测试通过！")
        print("AKID-NSGA2实施计划第6节验证完成。")
    else:
        print("❌ 部分验证测试失败！")
        print("请检查详细报告：test_results/final_report/")
    
    print("="*80)

if __name__ == "__main__":
    main()
'''
        
        return script
    
    def save_experimental_plan(self, filename: str = None):
        """保存实验计划到文件"""
        
        if filename is None:
            filename = 'experimental_design_plan.json'
        
        plan = self.generate_full_experimental_plan()
        
        # 创建输出目录
        os.makedirs('test_results/experimental_design/', exist_ok=True)
        filepath = os.path.join('test_results/experimental_design/', filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(plan, f, indent=2, ensure_ascii=False)
        
        # 同时保存实验执行脚本
        script_content = self.create_experiment_script()
        script_path = 'run_complete_validation.py'
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print(f"实验计划已保存: {filepath}")
        print(f"实验执行脚本已生成: {script_path}")
        
        return filepath, script_path


def main():
    """主函数"""
    print("="*80)
    print("AKID-NSGA2 实验设计生成器")
    print("="*80)
    print("按照实施计划第6.3节要求生成实验设计...")
    print()
    
    designer = ExperimentalDesigner()
    
    # 生成并保存实验计划
    plan_file, script_file = designer.save_experimental_plan()
    
    # 输出实验配置摘要
    plan = designer.generate_full_experimental_plan()
    
    print("实验配置摘要:")
    print("-" * 40)
    matrix = plan['experimental_matrix']
    print(f"算法配置数: {len(matrix['algorithms'])}")
    print(f"测试场景数: {len(matrix['scenarios'])}")
    print(f"每配置重复次数: {matrix['replications']}")
    print(f"总实验数: {matrix['total_experiments']}")
    print(f"预估总时长: {matrix['estimated_duration']}")
    
    print(f"\n算法列表: {', '.join(matrix['algorithms'])}")
    print(f"场景列表: {', '.join(matrix['scenarios'])}")
    
    print(f"\n验证指标:")
    primary_metrics = plan['evaluation_framework']['primary_metrics']
    print(f"主要指标: {', '.join(primary_metrics.keys())}")
    
    print(f"\n预期结果:")
    hypotheses = plan['expected_outcomes']['hypotheses']
    print(f"验证假设数: {len(hypotheses)}")
    
    print("\n" + "="*80)
    print("实验设计生成完成！")
    print("="*80)
    print(f"详细计划: {plan_file}")
    print(f"执行脚本: {script_file}")
    print("\n要开始完整验证，请运行：")
    print(f"python {script_file}")


if __name__ == "__main__":
    main() 