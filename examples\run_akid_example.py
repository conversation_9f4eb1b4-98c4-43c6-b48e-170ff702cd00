"""
AKID-NSGA2 快速运行示例

本脚本演示如何使用AKID-NSGA2增强算法进行机场滑行路径优化。
适合快速测试和验证AKID功能。
"""

import pandas as pd
import networkx as nx
import numpy as np
import os
from nsga2_core.algorithm import MARMT_RK_global, MARMT_RK_global_with_akid
from config import get_akid_config

def quick_load_data():
    """快速加载数据（简化版本）"""
    print("正在加载数据...")
    
    # 读取机场布局数据
    with open('doh1.txt', 'r') as file:
        lines = file.readlines()

    # 解析基本数据结构（简化处理）
    node_section_start = lines.index('%SECTION%1%;Nodes;\n') + 1
    edge_section_start = lines.index('%SECTION%2%;Edges;\n') + 1
    aircraft_section_start = lines.index('%SECTION%3%;Aircraft;\n') + 1

    # 节点数据
    node_data_lines = lines[node_section_start + 1:edge_section_start - 1]
    node_data = []
    for line in node_data_lines:
        if line.strip() and not line.startswith('%'):
            parts = line.strip().split(';')
            if len(parts) >= 4:
                node_id, x, y, specification = parts[1:5]
                node_data.append([node_id, float(x), float(y), specification])

    nodes_df = pd.DataFrame(node_data, columns=['Node ID', 'X', 'Y', 'Specification'])

    # 边数据
    edge_data_lines = lines[edge_section_start + 1:aircraft_section_start - 1]
    edge_data = []
    for line in edge_data_lines:
        if line.strip() and not line.startswith('%'):
            parts = line.strip().split(';')
            if len(parts) >= 6:
                edge_id, start_node, end_node, directed, length, edge_type = parts[1:7]
                edge_data.append([edge_id, start_node, end_node, directed, float(length), edge_type, []])

    edges_df = pd.DataFrame(edge_data, columns=['Edge ID', 'Start Node', 'End Node', 'Directed', 'Length', 'Type', 'Unavailable Time Windows'])

    # 飞机数据（限制数量以加快测试）
    aircraft_data_lines = lines[aircraft_section_start + 1:]
    aircraft_data = []
    count = 0
    max_aircraft = 5  # 限制飞机数量进行快速测试
    
    for line in aircraft_data_lines:
        if line.strip() and not line.startswith('%') and count < max_aircraft:
            parts = line.strip().split(';')
            if len(parts) >= 16:
                aircraft_type = parts[1].strip().strip("'")
                start_node = parts[2].strip().strip("'")
                end_node = parts[3].strip().strip("'")
                start_time_str = parts[4].strip("[]")
                start_time_values = start_time_str.split(';')
                start_time = float(start_time_values[0].strip())
                weight_class = parts[16].strip().strip("'")
                aircraft_data.append([aircraft_type, start_node, end_node, start_time, weight_class])
                count += 1

    aircraft_df = pd.DataFrame(aircraft_data, columns=['Type', 'Start Node', 'End Node', 'Start Time', 'Weight Class'])
    aircraft_df = aircraft_df.sort_values(by='Start Time').reset_index(drop=True)
    aircraft_df['Aircraft ID'] = aircraft_df.index

    # 速度配置数据
    speed_profile_df = pd.read_csv('doh1_database.csv')

    # 创建机场图
    airport_graph = nx.Graph()
    for _, edge in edges_df.iterrows():
        start_node = edge['Start Node']
        end_node = edge['End Node']
        length = edge['Length']
        time_window = edge['Unavailable Time Windows']
        airport_graph.add_edge(start_node, end_node, length=length, unavailable_time_windows=time_window)
        airport_graph.add_edge(end_node, start_node, length=length, unavailable_time_windows=time_window)

    print(f"数据加载完成: {len(nodes_df)}个节点, {len(edges_df)}条边, {len(aircraft_df)}架飞机")
    return aircraft_df, airport_graph, nodes_df, edges_df, speed_profile_df

def run_comparison_example():
    """运行AKID-NSGA2与标准NSGA-II的对比示例"""
    
    print("="*60)
    print("AKID-NSGA2 vs 标准NSGA-II 对比示例")
    print("="*60)
    
    # 加载数据
    aircraft_df, airport_graph, nodes_df, edges_df, speed_profile_df = quick_load_data()
    
    # 创建输出文件夹
    output_folder = 'example_results/'
    os.makedirs(output_folder, exist_ok=True)
    
    results = {}
    
    # 1. 运行标准NSGA-II
    print("\n1. 运行标准NSGA-II算法...")
    try:
        start_time = pd.Timestamp.now()
        pf_standard, ps_standard = MARMT_RK_global(
            aircraft_df, airport_graph, nodes_df, edges_df, speed_profile_df,
            run_index=1, output_folder=output_folder + 'standard_nsga2/',
            use_warmup_initialization=False  # 快速测试不使用预热
        )
        end_time = pd.Timestamp.now()
        
        results['标准NSGA-II'] = {
            'pareto_front_size': len(pf_standard),
            'best_time': min([p[0] for p in pf_standard]) if pf_standard else None,
            'best_fuel': min([p[1] for p in pf_standard]) if pf_standard else None,
            'runtime': (end_time - start_time).total_seconds()
        }
        print(f"  ✅ 完成! 帕累托前沿大小: {len(pf_standard)}")
        
    except Exception as e:
        print(f"  ❌ 失败: {str(e)}")
        results['标准NSGA-II'] = {'error': str(e)}
    
    # 2. 运行AKID-NSGA2 (默认配置)
    print("\n2. 运行AKID-NSGA2算法 (默认配置)...")
    try:
        start_time = pd.Timestamp.now()
        akid_config = get_akid_config('default')
        
        result = MARMT_RK_global_with_akid(
            aircraft_df, airport_graph, nodes_df, edges_df, speed_profile_df,
            run_index=1, output_folder=output_folder + 'akid_nsga2_default/',
            use_akid=True, akid_config=akid_config,
            use_warmup_initialization=False  # 快速测试不使用预热
        )
        end_time = pd.Timestamp.now()
        
        if len(result) == 3:
            pf_akid, ps_akid, akid_report = result
        else:
            pf_akid, ps_akid = result
            akid_report = None
        
        results['AKID-NSGA2'] = {
            'pareto_front_size': len(pf_akid),
            'best_time': min([p[0] for p in pf_akid]) if pf_akid else None,
            'best_fuel': min([p[1] for p in pf_akid]) if pf_akid else None,
            'runtime': (end_time - start_time).total_seconds(),
            'akid_report': akid_report
        }
        print(f"  ✅ 完成! 帕累托前沿大小: {len(pf_akid)}")
        
        if akid_report:
            kb_stats = akid_report.get('knowledge_base', {})
            injection_stats = akid_report.get('knowledge_injection', {})
            print(f"  📊 知识库最终大小: {kb_stats.get('final_size', 'N/A')}")
            print(f"  📊 知识注入次数: {injection_stats.get('total_injections', 'N/A')}")
        
    except Exception as e:
        print(f"  ❌ 失败: {str(e)}")
        results['AKID-NSGA2'] = {'error': str(e)}
    
    # 3. 运行AKID-NSGA2 (激进配置)
    print("\n3. 运行AKID-NSGA2算法 (激进配置)...")
    try:
        start_time = pd.Timestamp.now()
        akid_config = get_akid_config('aggressive')
        
        result = MARMT_RK_global_with_akid(
            aircraft_df, airport_graph, nodes_df, edges_df, speed_profile_df,
            run_index=1, output_folder=output_folder + 'akid_nsga2_aggressive/',
            use_akid=True, akid_config=akid_config,
            use_warmup_initialization=False
        )
        end_time = pd.Timestamp.now()
        
        if len(result) == 3:
            pf_akid_agg, ps_akid_agg, akid_report_agg = result
        else:
            pf_akid_agg, ps_akid_agg = result
            akid_report_agg = None
        
        results['AKID-NSGA2(激进)'] = {
            'pareto_front_size': len(pf_akid_agg),
            'best_time': min([p[0] for p in pf_akid_agg]) if pf_akid_agg else None,
            'best_fuel': min([p[1] for p in pf_akid_agg]) if pf_akid_agg else None,
            'runtime': (end_time - start_time).total_seconds(),
            'akid_report': akid_report_agg
        }
        print(f"  ✅ 完成! 帕累托前沿大小: {len(pf_akid_agg)}")
        
        if akid_report_agg:
            kb_stats = akid_report_agg.get('knowledge_base', {})
            injection_stats = akid_report_agg.get('knowledge_injection', {})
            print(f"  📊 知识库最终大小: {kb_stats.get('final_size', 'N/A')}")
            print(f"  📊 知识注入次数: {injection_stats.get('total_injections', 'N/A')}")
        
    except Exception as e:
        print(f"  ❌ 失败: {str(e)}")
        results['AKID-NSGA2(激进)'] = {'error': str(e)}
    
    # 4. 结果对比分析
    print("\n" + "="*60)
    print("结果对比分析")
    print("="*60)
    
    for algorithm, result in results.items():
        print(f"\n{algorithm}:")
        if 'error' in result:
            print(f"  ❌ 运行失败: {result['error']}")
        else:
            print(f"  📈 帕累托前沿大小: {result['pareto_front_size']}")
            print(f"  ⏱️  运行时间: {result['runtime']:.2f}秒")
            if result['best_time'] is not None:
                print(f"  🎯 最优时间: {result['best_time']:.2f}秒")
                print(f"  ⛽ 最优燃油: {result['best_fuel']:.2f}kg")
    
    # 5. 保存对比报告
    import json
    report_file = os.path.join(output_folder, 'comparison_report.json')
    with open(report_file, 'w', encoding='utf-8') as f:
        # 移除不能序列化的对象
        serializable_results = {}
        for algo, res in results.items():
            if 'akid_report' in res and res['akid_report']:
                # 简化AKID报告用于JSON序列化
                simplified_report = {
                    'knowledge_base_final_size': res['akid_report'].get('knowledge_base', {}).get('final_size', 0),
                    'total_injections': res['akid_report'].get('knowledge_injection', {}).get('total_injections', 0),
                    'average_diversity': res['akid_report'].get('diversity_monitoring', {}).get('average_diversity', 0)
                }
                res['akid_summary'] = simplified_report
                del res['akid_report']  # 移除复杂对象
            serializable_results[algo] = res
        
        json.dump(serializable_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细对比报告已保存: {report_file}")
    print(f"📁 所有结果文件保存在: {output_folder}")
    
    print("\n" + "="*60)
    print("示例运行完成!")
    print("="*60)

def main():
    """主函数"""
    print("AKID-NSGA2 快速运行示例")
    print("本示例将展示:")
    print("1. 标准NSGA-II算法运行")
    print("2. AKID-NSGA2默认配置运行")
    print("3. AKID-NSGA2激进配置运行")
    print("4. 性能对比分析")
    print()
    
    # 检查必要文件
    required_files = ['doh1.txt', 'doh1_database.csv']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ 缺失必要文件: {missing_files}")
        print("请确保数据文件存在后重新运行。")
        return
    
    try:
        run_comparison_example()
    except KeyboardInterrupt:
        print("\n用户中断运行")
    except Exception as e:
        print(f"\n❌ 运行出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 