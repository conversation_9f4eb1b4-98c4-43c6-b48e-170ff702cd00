"""
AKID核心模块 - 动态知识库(DKB)和多样性监控(DCMM)

本模块实现AKID-NSGA2算法的核心组件：
1. 动态知识库(DKB)：存储和管理优秀解
2. 多样性监控(DCMM)：监控种群多样性和收敛状态

主要功能：
- 知识库容量管理和质量评估
- 多样性指标计算和趋势分析
- 停滞检测和收敛判断
- 精英解提取和知识管理
"""

import numpy as np
import copy
from collections import deque
from typing import List, Tuple, Dict, Any, Optional, Union
import json
import os


class DynamicKnowledgeBase:
    """
    动态知识库：存储和管理优秀解
    
    主要功能：
    - 存储帕累托最优解和高质量解
    - 动态管理知识库容量
    - 基于质量和多样性的解评估
    - 支持知识库的导入导出
    """
    
    def __init__(self, capacity=50, elite_ratio=0.3, quality_weight=0.7, auto_cleanup=True):
        """
        初始化动态知识库
        
        参数:
            capacity: 知识库最大容量
            elite_ratio: 精英解比例
            quality_weight: 质量权重（相对于多样性）
            auto_cleanup: 是否自动清理低质量解
        """
        self.capacity = capacity
        self.elite_ratio = elite_ratio
        self.quality_weight = quality_weight
        self.auto_cleanup = auto_cleanup
        
        # 存储结构：[(individual, objectives, generation, quality_score)]
        self.solutions = []
        self.generation_counter = 0
        
        # 统计信息
        self.total_added = 0
        self.total_removed = 0
        
    def add_solution(self, individual: Dict, objectives: Tuple[float, float], 
                    generation: Optional[int] = None, quality_score: Optional[float] = None):
        """
        向知识库添加解
        
        参数:
            individual: 个体（字典格式）
            objectives: 目标函数值 (time, fuel)
            generation: 代数（可选）
            quality_score: 质量评分（可选，如果None则自动计算）
        """
        # 检查重复，使用更严格的容忍度
        if self._is_duplicate(individual, objectives, tolerance=1e-8):
            return False  # 返回False表示未添加（重复）
            
        # 计算质量评分
        if quality_score is None:
            quality_score = self._calculate_quality_score(objectives)
            
        # 如果generation为None，设置为0
        if generation is None:
            generation = 0
            
        # 创建解的副本
        individual_copy = copy.deepcopy(individual)
        
        # 添加到知识库
        self.solutions.append((individual_copy, objectives, generation, quality_score))
        self.total_added += 1
        
        # 检查容量
        if self.auto_cleanup and len(self.solutions) > self.capacity:
            self._auto_cleanup()
        elif len(self.solutions) > self.capacity:
            self._remove_oldest()
            
        return True  # 返回True表示成功添加
        
    def _calculate_quality_score(self, objectives: Tuple[float, float]) -> float:
        """
        计算解的质量评分
        
        基于目标函数值的归一化和综合评估
        """
        if not self.solutions:
            return 1.0
            
        # 获取当前知识库中的目标函数范围
        all_objectives = [sol[1] for sol in self.solutions]
        objectives_array = np.array(all_objectives)
        
        min_vals = np.min(objectives_array, axis=0)
        max_vals = np.max(objectives_array, axis=0)
        
        # 避免除零
        ranges = max_vals - min_vals
        ranges[ranges == 0] = 1.0
        
        # 归一化当前解的目标值（越小越好）
        normalized = (np.array(objectives) - min_vals) / ranges
        
        # 质量评分：基于归一化值的倒数（值越小，质量越高）
        quality = 1.0 / (1.0 + np.mean(normalized))
        
        return quality
        
    def _is_duplicate(self, individual: Dict, objectives: Tuple[float, float], 
                     tolerance: float = 1e-8) -> bool:
        """
        检查是否为重复解
        
        基于目标函数值的相似性判断
        """
        for _, stored_objectives, _, _ in self.solutions:
            if (abs(stored_objectives[0] - objectives[0]) < tolerance and 
                abs(stored_objectives[1] - objectives[1]) < tolerance):
                return True
        return False
        
    def _remove_oldest(self):
        """移除最老的解"""
        if self.solutions:
            self.solutions.pop(0)
            self.total_removed += 1
            
    def _auto_cleanup(self):
        """
        自动清理低质量解
        
        保留质量评分较高的解，移除低质量解
        """
        if len(self.solutions) <= self.capacity:
            return
            
        # 按质量评分排序
        self.solutions.sort(key=lambda x: x[3], reverse=True)
        
        # 保留前capacity个解
        removed_count = len(self.solutions) - self.capacity
        self.solutions = self.solutions[:self.capacity]
        self.total_removed += removed_count
        
    def get_elite_solutions(self, count: Optional[int] = None) -> List[Tuple]:
        """
        获取精英解
        
        参数:
            count: 返回的精英解数量，None表示使用elite_ratio
            
        返回:
            精英解列表：[(individual, objectives, generation)]
        """
        if not self.solutions:
            return []
            
        if count is None:
            count = max(1, int(len(self.solutions) * self.elite_ratio))
        
        count = min(count, len(self.solutions))
        
        # 提取非支配解
        nondominated = self._extract_nondominated()
        
        # 去除非支配解中的重复
        nondominated = self._remove_duplicates_from_solutions(nondominated)
        
        if len(nondominated) >= count:
            # 从非支配解中选择
            return self._select_diverse_solutions(nondominated, count)
        else:
            # 非支配解不足，补充高质量解
            remaining = count - len(nondominated)
            dominated = [sol for sol in self.solutions if sol not in nondominated]
            dominated.sort(key=lambda x: x[3], reverse=True)  # 按质量评分排序
            
            # 去除支配解中的重复
            dominated = self._remove_duplicates_from_solutions(dominated)
            
            result = nondominated.copy()
            result.extend(dominated[:remaining])
            return [(sol[0], sol[1], sol[2]) for sol in result]
            
    def _remove_duplicates_from_solutions(self, solutions: List[Tuple], tolerance: float = 1e-8) -> List[Tuple]:
        """
        从解列表中移除重复的解
        
        参数:
            solutions: 解列表
            tolerance: 容忍度
            
        返回:
            去重后的解列表
        """
        unique_solutions = []
        seen_objectives = []
        
        for solution in solutions:
            _, objectives, _, _ = solution
            is_duplicate = False
            
            for seen_obj in seen_objectives:
                if (abs(objectives[0] - seen_obj[0]) < tolerance and 
                    abs(objectives[1] - seen_obj[1]) < tolerance):
                    is_duplicate = True
                    break
                    
            if not is_duplicate:
                unique_solutions.append(solution)
                seen_objectives.append(objectives)
                
        return unique_solutions
        
    def _extract_nondominated(self) -> List[Tuple]:
        """
        提取非支配解
        
        返回:
            非支配解列表
        """
        if not self.solutions:
            return []
            
        objectives_list = [sol[1] for sol in self.solutions]
        nondominated_indices = []
        
        for i, obj1 in enumerate(objectives_list):
            is_dominated = False
            for j, obj2 in enumerate(objectives_list):
                if i != j:
                    # 检查obj2是否支配obj1
                    if (obj2[0] <= obj1[0] and obj2[1] <= obj1[1] and 
                        (obj2[0] < obj1[0] or obj2[1] < obj1[1])):
                        is_dominated = True
                        break
            if not is_dominated:
                nondominated_indices.append(i)
                
        return [self.solutions[i] for i in nondominated_indices]
        
    def _select_diverse_solutions(self, solutions: List[Tuple], count: int) -> List[Tuple]:
        """
        从解集中选择多样性较好的解
        
        使用拥挤度距离选择多样性好的解
        """
        if len(solutions) <= count:
            return [(sol[0], sol[1], sol[2]) for sol in solutions]
            
        # 计算拥挤度距离
        objectives = np.array([sol[1] for sol in solutions])
        crowding_distances = self._calculate_crowding_distance(objectives)
        
        # 按拥挤度距离排序
        indexed_solutions = list(enumerate(solutions))
        indexed_solutions.sort(key=lambda x: crowding_distances[x[0]], reverse=True)
        
        # 选择前count个
        selected = [indexed_solutions[i][1] for i in range(count)]
        return [(sol[0], sol[1], sol[2]) for sol in selected]
        
    def _calculate_crowding_distance(self, objectives: np.ndarray) -> np.ndarray:
        """
        计算拥挤度距离
        
        参数:
            objectives: 目标函数值矩阵 (n_solutions, n_objectives)
            
        返回:
            拥挤度距离数组
        """
        n_solutions, n_objectives = objectives.shape
        distances = np.zeros(n_solutions)
        
        for obj_idx in range(n_objectives):
            # 按当前目标排序
            sorted_indices = np.argsort(objectives[:, obj_idx])
            
            # 边界点设置为无穷大
            distances[sorted_indices[0]] = float('inf')
            distances[sorted_indices[-1]] = float('inf')
            
            # 计算目标函数范围
            obj_range = objectives[sorted_indices[-1], obj_idx] - objectives[sorted_indices[0], obj_idx]
            
            if obj_range > 0:
                # 计算中间点的拥挤度距离
                for i in range(1, n_solutions - 1):
                    distance = (objectives[sorted_indices[i + 1], obj_idx] - 
                              objectives[sorted_indices[i - 1], obj_idx]) / obj_range
                    distances[sorted_indices[i]] += distance
                    
        return distances
        
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取知识库统计信息
        
        返回:
            统计信息字典
        """
        if not self.solutions:
            return {
                'size': 0,
                'capacity': self.capacity,
                'utilization': 0.0,
                'total_added': self.total_added,
                'total_removed': self.total_removed,
                'quality_range': (0, 0),
                'generation_range': (0, 0)
            }
            
        qualities = [sol[3] for sol in self.solutions]
        generations = [sol[2] for sol in self.solutions]
        
        return {
            'size': len(self.solutions),
            'capacity': self.capacity,
            'utilization': len(self.solutions) / self.capacity,
            'total_added': self.total_added,
            'total_removed': self.total_removed,
            'quality_range': (min(qualities), max(qualities)),
            'generation_range': (min(generations), max(generations)),
            'avg_quality': np.mean(qualities)
        }
    
    def get_size(self) -> int:
        """
        获取知识库当前大小（兼容性方法）
        
        返回:
            当前存储的解的数量
        """
        return len(self.solutions)
    
    def is_empty(self) -> bool:
        """
        检查知识库是否为空
        
        返回:
            是否为空
        """
        return len(self.solutions) == 0
    
    def is_full(self) -> bool:
        """
        检查知识库是否已满
        
        返回:
            是否已满
        """
        return len(self.solutions) >= self.capacity
        
    def clear(self):
        """清空知识库"""
        self.solutions.clear()
        self.generation_counter = 0
        
    def save_to_file(self, filepath: str):
        """
        保存知识库到文件
        
        参数:
            filepath: 保存路径
        """
        data = {
            'solutions': [(sol[0], sol[1], sol[2], sol[3]) for sol in self.solutions],
            'capacity': self.capacity,
            'elite_ratio': self.elite_ratio,
            'quality_weight': self.quality_weight,
            'total_added': self.total_added,
            'total_removed': self.total_removed,
            'generation_counter': self.generation_counter
        }
        
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)
            
    def load_from_file(self, filepath: str):
        """
        从文件加载知识库
        
        参数:
            filepath: 文件路径
        """
        with open(filepath, 'r') as f:
            data = json.load(f)
            
        self.solutions = [(sol[0], tuple(sol[1]), sol[2], sol[3]) for sol in data['solutions']]
        self.capacity = data['capacity']
        self.elite_ratio = data['elite_ratio']
        self.quality_weight = data['quality_weight']
        self.total_added = data['total_added']
        self.total_removed = data['total_removed']
        self.generation_counter = data['generation_counter']


class DiversityMonitor:
    """
    多样性监控：监控种群多样性和收敛状态
    
    主要功能：
    - 种群目标空间多样性计算
    - 收敛趋势分析
    - 停滞检测
    - 知识注入时机判断
    """
    
    def __init__(self, window_size=5, stagnation_threshold=3, 
                 diversity_threshold=0.1, convergence_threshold=0.001):
        """
        初始化多样性监控器
        
        参数:
            window_size: 监控窗口大小
            stagnation_threshold: 停滞检测阈值（代数）
            diversity_threshold: 多样性阈值
            convergence_threshold: 收敛阈值
        """
        self.window_size = window_size
        self.stagnation_threshold = stagnation_threshold
        self.diversity_threshold = diversity_threshold
        self.convergence_threshold = convergence_threshold
        
        # 监控历史数据
        self.diversity_history = deque(maxlen=window_size)
        self.convergence_history = deque(maxlen=window_size)
        self.hypervolume_history = deque(maxlen=window_size)
        
        # 状态标记
        self.stagnation_counter = 0
        self.last_improvement_generation = 0
        
    def update_metrics(self, population_objectives: List[Tuple[float, float]], 
                      generation: int, reference_point: Optional[Tuple[float, float]] = None):
        """
        更新多样性和收敛指标
        
        参数:
            population_objectives: 种群目标函数值列表
            generation: 当前代数
            reference_point: 超体积计算的参考点
        """
        if not population_objectives:
            return
            
        objectives_array = np.array(population_objectives)
        
        # 计算多样性指标
        diversity = self._calculate_diversity(objectives_array)
        self.diversity_history.append(diversity)
        
        # 计算收敛指标
        convergence = self._calculate_convergence_metric(objectives_array)
        self.convergence_history.append(convergence)
        
        # 计算超体积（如果提供参考点）
        if reference_point is not None:
            hypervolume = self._calculate_hypervolume(objectives_array, reference_point)
            self.hypervolume_history.append(hypervolume)
            
        # 更新停滞检测
        self._update_stagnation_detection(generation)
        
    def _calculate_diversity(self, objectives: np.ndarray) -> float:
        """
        计算种群目标空间多样性
        
        使用目标函数值的标准差的平均值作为多样性指标
        """
        if objectives.shape[0] <= 1:
            return 0.0
            
        # 计算每个目标的标准差
        std_devs = np.std(objectives, axis=0)
        
        # 返回标准差的平均值作为多样性指标
        return np.mean(std_devs)
        
    def _calculate_convergence_metric(self, objectives: np.ndarray) -> float:
        """
        计算收敛指标
        
        使用目标函数值的变异系数作为收敛指标
        """
        if objectives.shape[0] <= 1:
            return 1.0
            
        # 计算每个目标的变异系数
        means = np.mean(objectives, axis=0)
        stds = np.std(objectives, axis=0)
        
        # 避免除零
        means[means == 0] = 1e-10
        
        coeffs_of_variation = stds / means
        return np.mean(coeffs_of_variation)
        
    def _calculate_hypervolume(self, objectives: np.ndarray, 
                              reference_point: Tuple[float, float]) -> float:
        """
        计算超体积指标
        
        简化的超体积计算，适用于二目标问题
        """
        if objectives.shape[0] == 0:
            return 0.0
            
        # 提取非支配解
        pareto_front = self._extract_pareto_front(objectives)
        
        if len(pareto_front) == 0:
            return 0.0
            
        # 按第一个目标排序
        pareto_front = pareto_front[np.argsort(pareto_front[:, 0])]
        
        # 计算超体积
        hypervolume = 0.0
        prev_x = 0.0
        
        for i, point in enumerate(pareto_front):
            if point[0] < reference_point[0] and point[1] < reference_point[1]:
                width = point[0] - prev_x
                height = reference_point[1] - point[1]
                hypervolume += width * height
                prev_x = point[0]
                
        return hypervolume
        
    def _extract_pareto_front(self, objectives: np.ndarray) -> np.ndarray:
        """
        提取帕累托前沿
        
        返回非支配解的目标函数值
        """
        n_solutions = objectives.shape[0]
        is_pareto = np.ones(n_solutions, dtype=bool)
        
        for i in range(n_solutions):
            for j in range(n_solutions):
                if i != j:
                    # 检查j是否支配i
                    if (np.all(objectives[j] <= objectives[i]) and 
                        np.any(objectives[j] < objectives[i])):
                        is_pareto[i] = False
                        break
                        
        return objectives[is_pareto]
        
    def _update_stagnation_detection(self, generation: int):
        """
        更新停滞检测
        
        检测种群是否出现停滞现象
        """
        # 检查是否有改进
        if len(self.diversity_history) >= 2:
            current_diversity = self.diversity_history[-1]
            previous_diversity = self.diversity_history[-2]
            
            # 如果多样性有显著改善，重置停滞计数器
            if current_diversity > previous_diversity * (1 + self.convergence_threshold):
                self.stagnation_counter = 0
                self.last_improvement_generation = generation
            else:
                self.stagnation_counter += 1
                
    def should_inject_knowledge(self, generation: Optional[int] = None, 
                               force_check: bool = False, 
                               diversity: Optional[float] = None, 
                               threshold: Optional[float] = None) -> Union[Tuple[bool, str], bool]:
        """
        判断是否需要注入知识（增强兼容性）
        
        参数:
            generation: 当前代数
            force_check: 强制检查
            diversity: 多样性值（兼容性参数）
            threshold: 阈值（兼容性参数）
            
        返回:
            如果提供了diversity参数，返回bool；否则返回(bool, str)
        """
        # 兼容旧接口调用
        if diversity is not None:
            if threshold is None:
                threshold = self.diversity_threshold
            return diversity < threshold
        
        # 新接口调用
        current_diversity = self.get_current_diversity()
        
        # 检查多样性过低
        if current_diversity < self.diversity_threshold:
            return True, "low_diversity"
        
        # 检查停滞
        if self.stagnation_counter >= self.stagnation_threshold:
            return True, "stagnation"
        
        # 定期注入
        if generation is not None and generation % 20 == 0 and generation > 0:
            return True, "periodic"
        
        # 强制检查
        if force_check:
            return True, "forced"
        
        return False, "no_injection_needed"
        
    def get_current_diversity(self) -> float:
        """获取当前多样性指标"""
        return self.diversity_history[-1] if self.diversity_history else 0.0
        
    def get_current_convergence(self) -> float:
        """获取当前收敛指标"""
        return self.convergence_history[-1] if self.convergence_history else 1.0
        
    def get_trend_analysis(self) -> Dict[str, Any]:
        """
        获取趋势分析结果
        
        返回:
            趋势分析字典
        """
        if len(self.diversity_history) < 2:
            return {
                'diversity_trend': 'insufficient_data',
                'convergence_trend': 'insufficient_data',
                'stagnation_detected': False,
                'recommendation': 'continue'
            }
            
        # 多样性趋势
        diversity_trend = 'stable'
        if len(self.diversity_history) >= 3:
            recent_diversity = list(self.diversity_history)[-3:]
            if recent_diversity[-1] > recent_diversity[0] * 1.1:
                diversity_trend = 'increasing'
            elif recent_diversity[-1] < recent_diversity[0] * 0.9:
                diversity_trend = 'decreasing'
                
        # 收敛趋势
        convergence_trend = 'stable'
        if len(self.convergence_history) >= 3:
            recent_convergence = list(self.convergence_history)[-3:]
            if recent_convergence[-1] < recent_convergence[0] * 0.9:
                convergence_trend = 'improving'
            elif recent_convergence[-1] > recent_convergence[0] * 1.1:
                convergence_trend = 'deteriorating'
                
        # 建议
        recommendation = 'continue'
        if diversity_trend == 'decreasing' and self.stagnation_counter >= 2:
            recommendation = 'inject_knowledge'
        elif convergence_trend == 'deteriorating':
            recommendation = 'increase_diversity'
            
        return {
            'diversity_trend': diversity_trend,
            'convergence_trend': convergence_trend,
            'stagnation_detected': self.stagnation_counter >= self.stagnation_threshold,
            'stagnation_duration': self.stagnation_counter,
            'recommendation': recommendation,
            'current_diversity': self.get_current_diversity(),
            'current_convergence': self.get_current_convergence()
        }
        
    def reset_stagnation(self):
        """重置停滞检测状态"""
        self.stagnation_counter = 0
        
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取多样性监控统计信息
        
        返回:
            统计信息字典
        """
        return {
            'current_diversity': self.get_current_diversity(),
            'current_convergence': self.get_current_convergence(),
            'diversity_history': list(self.diversity_history),
            'convergence_history': list(self.convergence_history),
            'hypervolume_history': list(self.hypervolume_history),
            'stagnation_counter': self.stagnation_counter,
            'last_improvement_generation': self.last_improvement_generation,
            'trend_analysis': self.get_trend_analysis()
        }
    
    def calculate_diversity(self, population_objectives: List[Tuple[float, float]]) -> float:
        """
        计算种群多样性（兼容性方法）
        
        参数:
            population_objectives: 种群目标函数值列表
            
        返回:
            多样性值
        """
        if not population_objectives:
            return 0.0
        objectives_array = np.array(population_objectives)
        return self._calculate_diversity(objectives_array)
    
    def update_diversity_history(self, diversity: float):
        """
        更新多样性历史（兼容性方法）
        
        参数:
            diversity: 多样性值
        """
        self.diversity_history.append(diversity)
        if len(self.diversity_history) > self.window_size:
            self.diversity_history.popleft() 