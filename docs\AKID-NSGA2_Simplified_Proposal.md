# AKID-NSGA2 精简技术方案

## 1. 概述

本方案旨在实现"基于问题特性和进化阶段的自适应知识注入与多样性调控NSGA-II"（AKID-NSGA2）算法，突出科研创新点，避免过度工程化，便于直接编程和后续维护。

---

## 2. 核心模块与简化设计

### 2.1 动态知识库（DKB）

**功能**：存储进化过程中出现的优秀解，便于后续知识注入。

**数据结构**：
```python
class KnowledgeBase:
    def __init__(self, capacity=50):
        self.solutions = []  # [(individual, objectives)]
        self.capacity = capacity

    def add(self, individual, objectives):
        self.solutions.append((individual, objectives))
        if len(self.solutions) > self.capacity:
            self.solutions.pop(0)  # FIFO 淘汰
```
- 只存储解和目标值，去除复杂评分、合并、age等属性。

---

### 2.2 多样性监控与知识注入触发（DCMM）

**功能**：监控种群多样性，决定是否注入知识。

**实现方式**：
```python
def calculate_diversity(population):
    import numpy as np
    objectives = np.array([ind.objectives for ind in population])
    return np.mean(np.std(objectives, axis=0))

def should_inject_knowledge(diversity, threshold=0.1, generation=None):
    return diversity < threshold or (generation is not None and generation % 20 == 0)
```
- 只用目标空间方差衡量多样性，去除收敛速率、复杂历史追踪。

---

### 2.3 知识注入模块（AKIM）

**功能**：从知识库选出少量优秀解注入当前种群。

**实现方式**：
```python
def inject_knowledge(population, knowledge_base, num_to_inject=2):
    import random
    if not knowledge_base.solutions:
        return
    knowledge = random.sample(knowledge_base.solutions, min(num_to_inject, len(knowledge_base.solutions)))
    for i, (ind, obj) in enumerate(knowledge):
        population[-(i+1)].individual = ind
        population[-(i+1)].objectives = obj
```
- 固定注入数量，随机选解，直接替换种群中最差个体。

---

### 2.4 关键子路径识别与利用（KSPI）

**功能**：识别知识库中高频子路径，偶尔用于局部优化。

**实现方式**：
```python
def extract_key_segments(knowledge_base, min_length=3, min_freq=2):
    from collections import Counter
    segment_counter = Counter()
    for ind, _ in knowledge_base.solutions:
        for path in ind:  # 假设ind为多条路径
            for i in range(len(path) - min_length + 1):
                segment = tuple(path[i:i+min_length])
                segment_counter[segment] += 1
    return [seg for seg, freq in segment_counter.items() if freq >= min_freq]

def inject_key_segments(population, key_segments, prob=0.2):
    import random
    for ind in population:
        if random.random() < prob and key_segments:
            seg = random.choice(key_segments)
            for path in ind.individual:
                if len(path) >= len(seg):
                    start = random.randint(0, len(path) - len(seg))
                    path[start:start+len(seg)] = seg
```
- 只统计高频子路径，简单插入，无需复杂评分和合并。

---

## 3. 主流程集成

**主循环伪代码**：
```python
def main_loop():
    population = initialize_population()
    kb = KnowledgeBase()
    for gen in range(max_generations):
        offspring = generate_offspring(population)
        population = environmental_selection(population + offspring)
        # 每代将非支配前沿的极值解加入知识库
        for ind in get_pareto_front(population):
            kb.add(ind.individual, ind.objectives)
        # 多样性监控与知识注入
        diversity = calculate_diversity(population)
        if should_inject_knowledge(diversity, generation=gen):
            inject_knowledge(population, kb)
        # 子路径优化
        if gen % 20 == 0:
            key_segments = extract_key_segments(kb)
            inject_key_segments(population, key_segments)
```
- 所有操作都在主循环中一目了然，便于调试和扩展。

---

## 4. 参数建议

- 知识库容量：50
- 多样性阈值：0.1
- 知识注入频率：每20代或多样性低于阈值
- 注入数量：2
- 子路径最小长度：3
- 子路径最小频率：2
- 子路径注入概率：0.2

---

## 5. 方案优势

- **创新性**：动态知识注入、多样性调控、子路径利用
- **极简实现**：每个模块只用最直接的方式实现，便于理解和维护
- **参数少且静态**：只需关注少量参数，易于调整
- **主流程清晰**：所有操作都在主循环中，结构清爽

---

## 6. 结论

本方案保留了AKID-NSGA2的核心思想，极大简化了实现难度，适合直接编程和后续科研扩展。如需进一步细化某一部分实现，可随时补充。 