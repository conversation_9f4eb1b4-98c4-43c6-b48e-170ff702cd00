"""
AKID算法工具函数模块 - 辅助功能和工具函数

本模块提供AKID-NSGA2算法实现所需的各种工具函数：
1. 数据格式转换和验证
2. 性能指标计算
3. 配置管理和验证
4. 日志记录和调试工具
5. 统计分析工具

主要功能：
- AKID配置验证和管理
- 性能指标计算（超体积、IGD等）
- 数据转换和格式化
- 日志记录和调试支持
"""

import numpy as np
import logging
import json
import os
from typing import List, Tuple, Dict, Any, Optional, Union
import time
from functools import wraps


def validate_akid_config(config: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    验证AKID配置参数
    
    参数:
        config: AKID配置字典
        
    返回:
        (是否有效, 错误信息列表)
    """
    errors = []
    
    # 检查必需的顶级配置
    required_keys = ['enable_akid']
    for key in required_keys:
        if key not in config:
            errors.append(f"Missing required config key: {key}")
            
    if not config.get('enable_akid', False):
        return True, []  # 如果未启用AKID，则无需验证其他参数
        
    # 验证DKB配置
    if 'dkb_config' in config:
        dkb_config = config['dkb_config']
        
        if 'capacity' in dkb_config:
            if not isinstance(dkb_config['capacity'], int) or dkb_config['capacity'] <= 0:
                errors.append("DKB capacity must be a positive integer")
                
        if 'elite_ratio' in dkb_config:
            if not 0 < dkb_config['elite_ratio'] <= 1:
                errors.append("DKB elite_ratio must be between 0 and 1")
                
        if 'quality_weight' in dkb_config:
            if not 0 <= dkb_config['quality_weight'] <= 1:
                errors.append("DKB quality_weight must be between 0 and 1")
                
    # 验证DCMM配置
    if 'dcmm_config' in config:
        dcmm_config = config['dcmm_config']
        
        if 'window_size' in dcmm_config:
            if not isinstance(dcmm_config['window_size'], int) or dcmm_config['window_size'] <= 0:
                errors.append("DCMM window_size must be a positive integer")
                
        if 'stagnation_threshold' in dcmm_config:
            if not isinstance(dcmm_config['stagnation_threshold'], int) or dcmm_config['stagnation_threshold'] <= 0:
                errors.append("DCMM stagnation_threshold must be a positive integer")
                
        if 'diversity_threshold' in dcmm_config:
            if not isinstance(dcmm_config['diversity_threshold'], (int, float)) or dcmm_config['diversity_threshold'] < 0:
                errors.append("DCMM diversity_threshold must be non-negative")
                
    # 验证AKIM配置
    if 'akim_config' in config:
        akim_config = config['akim_config']
        
        if 'injection_ratio_range' in akim_config:
            ratio_range = akim_config['injection_ratio_range']
            if (not isinstance(ratio_range, (list, tuple)) or len(ratio_range) != 2 or
                not all(0 <= r <= 1 for r in ratio_range) or ratio_range[0] > ratio_range[1]):
                errors.append("AKIM injection_ratio_range must be [min, max] with 0 <= min <= max <= 1")
                
        if 'adaptation_learning_rate' in akim_config:
            if not 0 < akim_config['adaptation_learning_rate'] <= 1:
                errors.append("AKIM adaptation_learning_rate must be between 0 and 1")
                
    # 验证关键子路径配置
    if 'key_segment_config' in config:
        segment_config = config['key_segment_config']
        
        if 'min_length' in segment_config:
            if not isinstance(segment_config['min_length'], int) or segment_config['min_length'] <= 0:
                errors.append("Key segment min_length must be a positive integer")
                
        if 'min_frequency' in segment_config:
            if not isinstance(segment_config['min_frequency'], int) or segment_config['min_frequency'] <= 0:
                errors.append("Key segment min_frequency must be a positive integer")
                
    return len(errors) == 0, errors


def setup_akid_logger(name: str = "AKID", level: int = logging.INFO, 
                     log_file: Optional[str] = None) -> logging.Logger:
    """
    设置AKID专用日志记录器
    
    参数:
        name: 日志记录器名称
        level: 日志级别
        log_file: 日志文件路径
        
    返回:
        配置好的日志记录器
    """
    logger = logging.getLogger(name)
    
    # 避免重复添加处理器
    if logger.handlers:
        return logger
        
    logger.setLevel(level)
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器（如果指定了文件路径）
    if log_file:
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
    return logger


def timing_decorator(func):
    """
    函数执行时间装饰器
    
    用于测量AKID模块函数的执行时间
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        logger = logging.getLogger("AKID")
        logger.debug(f"{func.__name__} executed in {end_time - start_time:.4f} seconds")
        
        return result
    return wrapper


def calculate_hypervolume_2d(pareto_front: np.ndarray, 
                           reference_point: Tuple[float, float]) -> float:
    """
    计算二维帕累托前沿的超体积
    
    参数:
        pareto_front: 帕累托前沿点集 (n, 2)
        reference_point: 参考点 (ref_x, ref_y)
        
    返回:
        超体积值
    """
    if pareto_front.shape[0] == 0:
        return 0.0
        
    # 确保都是最小化问题
    front = pareto_front.copy()
    
    # 过滤支配参考点的解
    valid_mask = np.all(front < reference_point, axis=1)
    if not np.any(valid_mask):
        return 0.0
        
    front = front[valid_mask]
    
    # 按第一个目标排序
    sorted_indices = np.argsort(front[:, 0])
    front = front[sorted_indices]
    
    # 计算超体积
    hypervolume = 0.0
    prev_x = 0.0
    
    for point in front:
        width = point[0] - prev_x
        height = reference_point[1] - point[1]
        hypervolume += width * height
        prev_x = point[0]
        
    return hypervolume


def calculate_igd(pareto_front: np.ndarray, true_pareto_front: np.ndarray) -> float:
    """
    计算IGD (Inverted Generational Distance) 指标
    
    参数:
        pareto_front: 算法得到的帕累托前沿
        true_pareto_front: 真实帕累托前沿
        
    返回:
        IGD值
    """
    if true_pareto_front.shape[0] == 0:
        return float('inf')
        
    if pareto_front.shape[0] == 0:
        return float('inf')
        
    # 计算每个真实前沿点到算法前沿的最小距离
    distances = []
    
    for true_point in true_pareto_front:
        min_distance = float('inf')
        for front_point in pareto_front:
            distance = np.linalg.norm(true_point - front_point)
            min_distance = min(min_distance, distance)
        distances.append(min_distance)
        
    return np.mean(distances)


def calculate_gd(pareto_front: np.ndarray, true_pareto_front: np.ndarray) -> float:
    """
    计算GD (Generational Distance) 指标
    
    参数:
        pareto_front: 算法得到的帕累托前沿
        true_pareto_front: 真实帕累托前沿
        
    返回:
        GD值
    """
    if pareto_front.shape[0] == 0:
        return float('inf')
        
    if true_pareto_front.shape[0] == 0:
        return float('inf')
        
    # 计算每个算法前沿点到真实前沿的最小距离
    distances = []
    
    for front_point in pareto_front:
        min_distance = float('inf')
        for true_point in true_pareto_front:
            distance = np.linalg.norm(front_point - true_point)
            min_distance = min(min_distance, distance)
        distances.append(min_distance)
        
    return np.mean(distances)


def calculate_spacing_metric(pareto_front: np.ndarray) -> float:
    """
    计算分布均匀性指标
    
    参数:
        pareto_front: 帕累托前沿点集
        
    返回:
        分布均匀性值
    """
    if pareto_front.shape[0] <= 1:
        return 0.0
        
    n = pareto_front.shape[0]
    distances = []
    
    # 计算每个点到其他所有点的距离
    for i in range(n):
        min_distance = float('inf')
        for j in range(n):
            if i != j:
                distance = np.linalg.norm(pareto_front[i] - pareto_front[j])
                min_distance = min(min_distance, distance)
        distances.append(min_distance)
        
    # 计算分布均匀性
    mean_distance = np.mean(distances)
    variance = np.mean([(d - mean_distance) ** 2 for d in distances])
    
    return np.sqrt(variance)


def extract_nondominated_solutions(objectives: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
    """
    提取非支配解
    
    参数:
        objectives: 目标函数值矩阵 (n, m)
        
    返回:
        (非支配解索引, 非支配解目标值)
    """
    n = objectives.shape[0]
    is_dominated = np.zeros(n, dtype=bool)
    
    for i in range(n):
        for j in range(n):
            if i != j:
                # 检查j是否支配i
                if (np.all(objectives[j] <= objectives[i]) and 
                    np.any(objectives[j] < objectives[i])):
                    is_dominated[i] = True
                    break
                    
    nondominated_indices = np.where(~is_dominated)[0]
    nondominated_objectives = objectives[nondominated_indices]
    
    return nondominated_indices, nondominated_objectives


def normalize_objectives(objectives: np.ndarray, 
                        reference_min: Optional[np.ndarray] = None,
                        reference_max: Optional[np.ndarray] = None) -> np.ndarray:
    """
    归一化目标函数值
    
    参数:
        objectives: 目标函数值矩阵
        reference_min: 参考最小值
        reference_max: 参考最大值
        
    返回:
        归一化后的目标函数值
    """
    if reference_min is None:
        reference_min = np.min(objectives, axis=0)
    if reference_max is None:
        reference_max = np.max(objectives, axis=0)
        
    ranges = reference_max - reference_min
    ranges[ranges == 0] = 1.0  # 避免除零
    
    normalized = (objectives - reference_min) / ranges
    return normalized


def merge_akid_configs(base_config: Dict[str, Any], 
                      override_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    合并AKID配置
    
    参数:
        base_config: 基础配置
        override_config: 覆盖配置
        
    返回:
        合并后的配置
    """
    merged_config = base_config.copy()
    
    for key, value in override_config.items():
        if key in merged_config and isinstance(merged_config[key], dict) and isinstance(value, dict):
            # 递归合并字典
            merged_config[key] = merge_akid_configs(merged_config[key], value)
        else:
            merged_config[key] = value
            
    return merged_config


def format_akid_statistics(stats_dict: Dict[str, Any], 
                          precision: int = 4) -> str:
    """
    格式化AKID统计信息为可读字符串
    
    参数:
        stats_dict: 统计信息字典
        precision: 小数精度
        
    返回:
        格式化的字符串
    """
    def format_value(value):
        if isinstance(value, float):
            return f"{value:.{precision}f}"
        elif isinstance(value, dict):
            return "\n" + "\n".join([f"  {k}: {format_value(v)}" for k, v in value.items()])
        elif isinstance(value, list):
            if all(isinstance(x, (int, float)) for x in value):
                return f"[{', '.join([format_value(x) for x in value])}]"
            else:
                return str(value)
        else:
            return str(value)
    
    lines = []
    for key, value in stats_dict.items():
        formatted_value = format_value(value)
        lines.append(f"{key}: {formatted_value}")
        
    return "\n".join(lines)


def save_akid_results(results: Dict[str, Any], filepath: str, 
                     format: str = 'json') -> bool:
    """
    保存AKID结果到文件
    
    参数:
        results: 结果数据
        filepath: 保存路径
        format: 保存格式 ('json', 'npz', 'csv')
        
    返回:
        是否保存成功
    """
    try:
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        if format == 'json':
            # 处理numpy数组
            def convert_numpy(obj):
                if isinstance(obj, np.ndarray):
                    return obj.tolist()
                elif isinstance(obj, np.integer):
                    return int(obj)
                elif isinstance(obj, np.floating):
                    return float(obj)
                elif isinstance(obj, dict):
                    return {k: convert_numpy(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_numpy(item) for item in obj]
                else:
                    return obj
                    
            converted_results = convert_numpy(results)
            
            with open(filepath, 'w') as f:
                json.dump(converted_results, f, indent=2)
                
        elif format == 'npz':
            # 只保存numpy数组数据
            numpy_data = {}
            for key, value in results.items():
                if isinstance(value, np.ndarray):
                    numpy_data[key] = value
                elif isinstance(value, list):
                    try:
                        numpy_data[key] = np.array(value)
                    except:
                        pass
                        
            np.savez_compressed(filepath, **numpy_data)
            
        elif format == 'csv':
            import pandas as pd
            
            # 尝试将结果转换为DataFrame
            if 'pareto_front' in results and isinstance(results['pareto_front'], np.ndarray):
                df = pd.DataFrame(results['pareto_front'], 
                                columns=[f'objective_{i}' for i in range(results['pareto_front'].shape[1])])
                df.to_csv(filepath, index=False)
            else:
                # 如果无法转换为表格，则回退到JSON格式
                return save_akid_results(results, filepath.replace('.csv', '.json'), 'json')
                
        return True
        
    except Exception as e:
        logger = logging.getLogger("AKID")
        logger.error(f"Failed to save AKID results to {filepath}: {e}")
        return False


def load_akid_results(filepath: str) -> Optional[Dict[str, Any]]:
    """
    从文件加载AKID结果
    
    参数:
        filepath: 文件路径
        
    返回:
        加载的结果数据
    """
    try:
        if filepath.endswith('.json'):
            with open(filepath, 'r') as f:
                return json.load(f)
                
        elif filepath.endswith('.npz'):
            data = np.load(filepath, allow_pickle=True)
            return {key: data[key] for key in data.files}
            
        elif filepath.endswith('.csv'):
            import pandas as pd
            df = pd.read_csv(filepath)
            return {'data': df.values, 'columns': df.columns.tolist()}
            
        else:
            logger = logging.getLogger("AKID")
            logger.warning(f"Unsupported file format: {filepath}")
            return None
            
    except Exception as e:
        logger = logging.getLogger("AKID")
        logger.error(f"Failed to load AKID results from {filepath}: {e}")
        return None


def create_akid_summary_report(knowledge_base, diversity_monitor, 
                              knowledge_injector, segment_analyzer,
                              final_pareto_front: np.ndarray) -> Dict[str, Any]:
    """
    创建AKID算法执行总结报告
    
    参数:
        knowledge_base: 动态知识库
        diversity_monitor: 多样性监控器
        knowledge_injector: 知识注入器
        segment_analyzer: 路径段分析器
        final_pareto_front: 最终帕累托前沿
        
    返回:
        总结报告字典
    """
    report = {
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'knowledge_base_stats': knowledge_base.get_statistics() if knowledge_base else {},
        'diversity_monitor_stats': diversity_monitor.get_statistics() if diversity_monitor else {},
        'injection_stats': knowledge_injector.get_injection_statistics() if knowledge_injector else {},
        'segment_analysis_stats': segment_analyzer.get_statistics() if segment_analyzer else {},
        'final_results': {
            'pareto_front_size': len(final_pareto_front) if final_pareto_front is not None else 0,
            'hypervolume': 0.0,  # 需要提供参考点来计算
            'diversity': calculate_spacing_metric(final_pareto_front) if final_pareto_front is not None else 0.0
        }
    }
    
    # 计算趋势分析
    if diversity_monitor:
        trend_analysis = diversity_monitor.get_trend_analysis()
        report['trend_analysis'] = trend_analysis
        
    return report


class AKIDProfiler:
    """
    AKID算法性能分析器
    
    用于分析和记录AKID各模块的性能表现
    """
    
    def __init__(self):
        self.timing_data = {}
        self.call_counts = {}
        
    def record_timing(self, module_name: str, operation: str, duration: float):
        """记录模块操作的执行时间"""
        key = f"{module_name}.{operation}"
        
        if key not in self.timing_data:
            self.timing_data[key] = []
            self.call_counts[key] = 0
            
        self.timing_data[key].append(duration)
        self.call_counts[key] += 1
        
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能总结"""
        summary = {}
        
        for key, times in self.timing_data.items():
            summary[key] = {
                'total_calls': self.call_counts[key],
                'total_time': sum(times),
                'average_time': np.mean(times),
                'min_time': min(times),
                'max_time': max(times),
                'std_time': np.std(times)
            }
            
        return summary
        
    def clear(self):
        """清空性能数据"""
        self.timing_data.clear()
        self.call_counts.clear()


# 全局性能分析器实例
akid_profiler = AKIDProfiler() 