"""
知识注入模块(AKIM) - 自适应知识注入和适应性调控

本模块实现AKID-NSGA2算法的知识注入功能：
1. 自适应知识注入模块(AKIM)：智能化知识注入策略
2. 注入时机判断和注入量自适应调整
3. 多种注入策略和效果评估
4. 注入历史记录和成功率统计

主要功能：
- 动态调整注入比例和策略
- 基于多样性和收敛状态的智能注入
- 注入效果评估和策略优化
- 支持多种注入模式（替换、混合、边界注入）
"""

import numpy as np
import random
import copy
from typing import List, Tuple, Dict, Any, Optional, Union
from collections import deque
import logging


class KnowledgeInjector:
    """
    知识注入器：自适应注入知识库中的优秀解
    
    主要功能：
    - 智能化注入时机判断
    - 自适应注入比例调整
    - 多种注入策略选择
    - 注入效果评估和反馈
    """
    
    def __init__(self, min_injection_ratio=0.05, max_injection_ratio=0.2, 
                 adaptation_learning_rate=0.1, success_threshold=0.7):
        """
        初始化知识注入器
        
        参数:
            min_injection_ratio: 最小注入比例
            max_injection_ratio: 最大注入比例
            adaptation_learning_rate: 自适应学习率
            success_threshold: 成功率阈值
        """
        self.min_injection_ratio = min_injection_ratio
        self.max_injection_ratio = max_injection_ratio
        self.adaptation_learning_rate = adaptation_learning_rate
        self.success_threshold = success_threshold
        
        # 自适应参数
        self.current_injection_ratio = (min_injection_ratio + max_injection_ratio) / 2
        
        # 注入策略权重
        self.strategy_weights = {
            'diversity_based': 1.0,
            'quality_based': 1.0,
            'boundary_based': 0.8,
            'hybrid': 1.2
        }
        
        # 历史记录
        self.injection_history = deque(maxlen=50)
        self.success_history = deque(maxlen=20)
        
        # 统计信息
        self.total_injections = 0
        self.successful_injections = 0
        
    def inject_knowledge(self, population: List[Dict], fitness_values: List[Tuple], 
                        knowledge_base, diversity_monitor, generation: int,
                        injection_reason: str = "manual") -> Tuple[List[Dict], List[Tuple], Dict]:
        """
        向种群注入知识
        
        参数:
            population: 当前种群
            fitness_values: 种群适应度值
            knowledge_base: 动态知识库
            diversity_monitor: 多样性监控器
            generation: 当前代数
            injection_reason: 注入原因
            
        返回:
            (新种群, 新适应度值, 注入统计信息)
        """
        if not knowledge_base.solutions:
            return population, fitness_values, {
                'injected_count': 0,
                'injection_ratio': 0.0,
                'strategy_used': 'none',
                'reason': 'empty_knowledge_base'
            }
            
        # 确定注入策略
        strategy = self._select_injection_strategy(diversity_monitor, injection_reason)
        
        # 确定注入数量
        injection_count = self._determine_injection_count(
            len(population), diversity_monitor, injection_reason)
        
        # 获取知识库中的优秀解
        elite_solutions = knowledge_base.get_elite_solutions(injection_count * 2)  # 获取更多候选
        
        if not elite_solutions:
            return population, fitness_values, {
                'injected_count': 0,
                'injection_ratio': 0.0,
                'strategy_used': strategy,
                'reason': 'no_elite_solutions'
            }
            
        # 执行注入
        new_population, new_fitness, injection_stats = self._execute_injection(
            population, fitness_values, elite_solutions, injection_count, strategy)
        
        # 记录注入历史
        self._record_injection(generation, injection_count, strategy, injection_reason, injection_stats)
        
        # 更新统计
        self.total_injections += 1
        
        return new_population, new_fitness, injection_stats
        
    def _select_injection_strategy(self, diversity_monitor, injection_reason: str) -> str:
        """
        选择注入策略
        
        基于当前种群状态和注入原因选择最适合的策略
        """
        if injection_reason == "low_diversity":
            return "diversity_based"
        elif injection_reason == "stagnation":
            return "hybrid"
        elif injection_reason == "over_convergence":
            return "boundary_based"
        elif injection_reason == "periodic":
            return "quality_based"
        else:
            # 基于历史成功率动态选择
            return self._select_strategy_by_success_rate()
            
    def _select_strategy_by_success_rate(self) -> str:
        """
        基于历史成功率选择策略
        """
        if not self.injection_history:
            return "hybrid"
            
        # 统计各策略的成功率
        strategy_stats = {}
        for record in self.injection_history:
            strategy = record['strategy']
            success = record.get('success', False)
            
            if strategy not in strategy_stats:
                strategy_stats[strategy] = {'total': 0, 'success': 0}
                
            strategy_stats[strategy]['total'] += 1
            if success:
                strategy_stats[strategy]['success'] += 1
                
        # 计算加权成功率
        best_strategy = "hybrid"
        best_score = 0.0
        
        for strategy, stats in strategy_stats.items():
            if stats['total'] > 0:
                success_rate = stats['success'] / stats['total']
                weight = self.strategy_weights.get(strategy, 1.0)
                score = success_rate * weight
                
                if score > best_score:
                    best_score = score
                    best_strategy = strategy
                    
        return best_strategy
        
    def _determine_injection_count(self, population_size: int, diversity_monitor, 
                                 injection_reason: str) -> int:
        """
        确定注入数量
        
        基于种群大小、多样性状态和注入原因自适应确定注入数量
        """
        # 基础注入比例
        base_ratio = self.current_injection_ratio
        
        # 根据注入原因调整
        if injection_reason == "low_diversity":
            ratio = min(base_ratio * 1.5, self.max_injection_ratio)
        elif injection_reason == "stagnation":
            ratio = min(base_ratio * 2.0, self.max_injection_ratio)
        elif injection_reason == "periodic":
            ratio = base_ratio * 0.8
        else:
            ratio = base_ratio
            
        # 根据多样性状态微调
        if diversity_monitor:
            current_diversity = diversity_monitor.get_current_diversity()
            if current_diversity < diversity_monitor.diversity_threshold * 0.5:
                ratio *= 1.3  # 多样性极低时增加注入量
            elif current_diversity > diversity_monitor.diversity_threshold * 1.5:
                ratio *= 0.7  # 多样性较高时减少注入量
                
        # 确保在合理范围内
        ratio = max(self.min_injection_ratio, min(ratio, self.max_injection_ratio))
        injection_count = max(1, int(population_size * ratio))
        
        return min(injection_count, population_size // 2)  # 最多替换一半种群
        
    def _execute_injection(self, population: List[Dict], fitness_values: List[Tuple],
                          elite_solutions: List[Tuple], injection_count: int,
                          strategy: str) -> Tuple[List[Dict], List[Tuple], Dict]:
        """
        执行具体的注入操作
        
        根据选择的策略执行注入
        """
        if strategy == "diversity_based":
            return self._diversity_based_injection(
                population, fitness_values, elite_solutions, injection_count)
        elif strategy == "quality_based":
            return self._quality_based_injection(
                population, fitness_values, elite_solutions, injection_count)
        elif strategy == "boundary_based":
            return self._boundary_based_injection(
                population, fitness_values, elite_solutions, injection_count)
        elif strategy == "hybrid":
            return self._hybrid_injection(
                population, fitness_values, elite_solutions, injection_count)
        else:
            return self._quality_based_injection(
                population, fitness_values, elite_solutions, injection_count)
            
    def _check_solution_duplicate(self, solution_individual: Dict, solution_objectives: Tuple[float, float],
                                 population: List[Dict], fitness_values: List[Tuple], 
                                 tolerance: float = 1e-6) -> bool:
        """
        检查解是否与种群中的现有解重复
        
        参数:
            solution_individual: 候选解的个体
            solution_objectives: 候选解的目标函数值  
            population: 当前种群
            fitness_values: 种群适应度值
            tolerance: 容忍度
            
        返回:
            True表示重复，False表示不重复
        """
        for fitness in fitness_values:
            if (abs(fitness[0] - solution_objectives[0]) < tolerance and 
                abs(fitness[1] - solution_objectives[1]) < tolerance):
                return True
        return False

    def _filter_duplicate_elites(self, elite_solutions: List[Tuple], 
                                population: List[Dict], fitness_values: List[Tuple],
                                tolerance: float = 1e-6) -> List[Tuple]:
        """
        过滤重复的精英解
        
        移除与当前种群中解重复的精英解
        
        参数:
            elite_solutions: 精英解列表
            population: 当前种群
            fitness_values: 种群适应度值
            tolerance: 容忍度
            
        返回:
            过滤后的精英解列表
        """
        filtered_elites = []
        
        for elite_ind, elite_obj, elite_gen in elite_solutions:
            if not self._check_solution_duplicate(elite_ind, elite_obj, population, fitness_values, tolerance):
                filtered_elites.append((elite_ind, elite_obj, elite_gen))
                
        return filtered_elites

    def _diversity_based_injection(self, population: List[Dict], fitness_values: List[Tuple],
                                 elite_solutions: List[Tuple], injection_count: int
                                 ) -> Tuple[List[Dict], List[Tuple], Dict]:
        """
        基于多样性的注入策略
        
        选择能够增加种群多样性的解进行注入
        """
        # 复制种群和适应度
        new_population = copy.deepcopy(population)
        new_fitness = copy.deepcopy(fitness_values)
        
        # 过滤重复的精英解
        filtered_elites = self._filter_duplicate_elites(elite_solutions, population, fitness_values)
        
        if not filtered_elites:
            return new_population, new_fitness, {
                'injected_count': 0,
                'injection_ratio': 0.0,
                'strategy_used': 'diversity_based',
                'selected_elites': 0,
                'replacement_method': 'no_valid_elites'
            }
        
        # 计算当前种群的目标空间覆盖范围
        current_objectives = np.array([f[:2] for f in fitness_values])
        
        # 选择多样性贡献最大的精英解
        selected_elites = self._select_diverse_elites(
            filtered_elites, current_objectives, min(injection_count, len(filtered_elites)))
        
        # 找到要替换的个体（选择密集区域的个体）
        replacement_indices = self._find_crowded_individuals(
            current_objectives, len(selected_elites))
        
        # 执行替换
        injected_count = 0
        for i, (elite_ind, elite_obj, _) in enumerate(selected_elites):
            if i < len(replacement_indices):
                idx = replacement_indices[i]
                new_population[idx] = copy.deepcopy(elite_ind)
                new_fitness[idx] = elite_obj + (0,)  # 假设约束值为0
                injected_count += 1
                
        return new_population, new_fitness, {
            'injected_count': injected_count,
            'injection_ratio': injected_count / len(population),
            'strategy_used': 'diversity_based',
            'selected_elites': len(selected_elites),
            'replacement_method': 'crowded_replacement',
            'filtered_elites': len(filtered_elites),
            'original_elites': len(elite_solutions)
        }
        
    def _quality_based_injection(self, population: List[Dict], fitness_values: List[Tuple],
                               elite_solutions: List[Tuple], injection_count: int
                               ) -> Tuple[List[Dict], List[Tuple], Dict]:
        """
        基于质量的注入策略
        
        选择质量最高的解替换种群中最差的个体
        """
        # 复制种群和适应度
        new_population = copy.deepcopy(population)
        new_fitness = copy.deepcopy(fitness_values)
        
        # 过滤重复的精英解
        filtered_elites = self._filter_duplicate_elites(elite_solutions, population, fitness_values)
        
        if not filtered_elites:
            return new_population, new_fitness, {
                'injected_count': 0,
                'injection_ratio': 0.0,
                'strategy_used': 'quality_based',
                'selected_elites': 0,
                'replacement_method': 'no_valid_elites'
            }
        
        # 选择最佳的精英解
        selected_elites = filtered_elites[:min(injection_count, len(filtered_elites))]
        
        # 找到种群中最差的个体
        worst_indices = self._find_worst_individuals(fitness_values, len(selected_elites))
        
        # 执行替换
        injected_count = 0
        for i, (elite_ind, elite_obj, _) in enumerate(selected_elites):
            if i < len(worst_indices):
                idx = worst_indices[i]
                new_population[idx] = copy.deepcopy(elite_ind)
                new_fitness[idx] = elite_obj + (0,)  # 假设约束值为0
                injected_count += 1
                
        return new_population, new_fitness, {
            'injected_count': injected_count,
            'injection_ratio': injected_count / len(population),
            'strategy_used': 'quality_based',
            'selected_elites': len(selected_elites),
            'replacement_method': 'worst_replacement',
            'filtered_elites': len(filtered_elites),
            'original_elites': len(elite_solutions)
        }
        
    def _boundary_based_injection(self, population: List[Dict], fitness_values: List[Tuple],
                                elite_solutions: List[Tuple], injection_count: int
                                ) -> Tuple[List[Dict], List[Tuple], Dict]:
        """
        基于边界的注入策略
        
        在目标空间的边界区域注入解，增加边界多样性
        """
        # 复制种群和适应度
        new_population = copy.deepcopy(population)
        new_fitness = copy.deepcopy(fitness_values)
        
        # 过滤重复的精英解
        filtered_elites = self._filter_duplicate_elites(elite_solutions, population, fitness_values)
        
        if not filtered_elites:
            return new_population, new_fitness, {
                'injected_count': 0,
                'injection_ratio': 0.0,
                'strategy_used': 'boundary_based',
                'selected_elites': 0,
                'replacement_method': 'no_valid_elites'
            }
        
        # 选择边界特性明显的精英解
        selected_elites = self._select_boundary_elites(filtered_elites, min(injection_count, len(filtered_elites)))
        
        # 找到非边界的个体进行替换
        replacement_indices = self._find_non_boundary_individuals(
            fitness_values, len(selected_elites))
        
        # 执行替换
        injected_count = 0
        for i, (elite_ind, elite_obj, _) in enumerate(selected_elites):
            if i < len(replacement_indices):
                idx = replacement_indices[i]
                new_population[idx] = copy.deepcopy(elite_ind)
                new_fitness[idx] = elite_obj + (0,)  # 假设约束值为0
                injected_count += 1
                
        return new_population, new_fitness, {
            'injected_count': injected_count,
            'injection_ratio': injected_count / len(population),
            'strategy_used': 'boundary_based',
            'selected_elites': len(selected_elites),
            'replacement_method': 'non_boundary_replacement',
            'filtered_elites': len(filtered_elites),
            'original_elites': len(elite_solutions)
        }
        
    def _hybrid_injection(self, population: List[Dict], fitness_values: List[Tuple],
                        elite_solutions: List[Tuple], injection_count: int
                        ) -> Tuple[List[Dict], List[Tuple], Dict]:
        """
        混合注入策略
        
        结合多种策略的优点
        """
        # 分配注入配额
        quality_count = injection_count // 2
        diversity_count = injection_count - quality_count
        
        # 执行质量注入
        temp_pop, temp_fit, quality_stats = self._quality_based_injection(
            population, fitness_values, elite_solutions, quality_count)
        
        # 在质量注入基础上执行多样性注入
        remaining_elites = elite_solutions[quality_count:]
        final_pop, final_fit, diversity_stats = self._diversity_based_injection(
            temp_pop, temp_fit, remaining_elites, diversity_count)
        
        return final_pop, final_fit, {
            'injected_count': quality_stats['injected_count'] + diversity_stats['injected_count'],
            'injection_ratio': (quality_stats['injected_count'] + diversity_stats['injected_count']) / len(population),
            'strategy_used': 'hybrid',
            'quality_injection': quality_stats['injected_count'],
            'diversity_injection': diversity_stats['injected_count']
        }
        
    def _select_diverse_elites(self, elite_solutions: List[Tuple], 
                             current_objectives: np.ndarray, count: int) -> List[Tuple]:
        """
        选择多样性贡献最大的精英解
        """
        if len(elite_solutions) <= count:
            return elite_solutions
            
        elite_objectives = np.array([sol[1] for sol in elite_solutions])
        
        # 计算每个精英解对当前种群多样性的贡献
        diversity_contributions = []
        
        for elite_obj in elite_objectives:
            # 计算与当前种群的最小距离
            distances = np.sqrt(np.sum((current_objectives - elite_obj) ** 2, axis=1))
            min_distance = np.min(distances)
            diversity_contributions.append(min_distance)
            
        # 选择多样性贡献最大的解
        selected_indices = np.argsort(diversity_contributions)[-count:]
        return [elite_solutions[i] for i in selected_indices]
        
    def _select_boundary_elites(self, elite_solutions: List[Tuple], count: int) -> List[Tuple]:
        """
        选择边界特性明显的精英解
        """
        if len(elite_solutions) <= count:
            return elite_solutions
            
        elite_objectives = np.array([sol[1] for sol in elite_solutions])
        
        # 找到各目标的极值解
        boundary_indices = set()
        
        # 每个目标的最小值和最大值对应的解
        for obj_idx in range(elite_objectives.shape[1]):
            min_idx = np.argmin(elite_objectives[:, obj_idx])
            max_idx = np.argmax(elite_objectives[:, obj_idx])
            boundary_indices.add(min_idx)
            boundary_indices.add(max_idx)
            
        boundary_solutions = [elite_solutions[i] for i in boundary_indices]
        
        # 如果边界解不够，补充其他解
        if len(boundary_solutions) < count:
            remaining_indices = [i for i in range(len(elite_solutions)) 
                               if i not in boundary_indices]
            remaining_count = count - len(boundary_solutions)
            boundary_solutions.extend([elite_solutions[i] 
                                     for i in remaining_indices[:remaining_count]])
                                     
        return boundary_solutions[:count]
        
    def _find_crowded_individuals(self, objectives: np.ndarray, count: int) -> List[int]:
        """
        找到密集区域的个体
        """
        n_solutions = objectives.shape[0]
        crowding_scores = np.zeros(n_solutions)
        
        # 计算每个个体周围的密度
        for i in range(n_solutions):
            distances = np.sqrt(np.sum((objectives - objectives[i]) ** 2, axis=1))
            # 排除自身，计算到最近邻的平均距离
            distances[i] = np.inf
            k_nearest = min(5, n_solutions - 1)  # 考虑5个最近邻
            nearest_distances = np.partition(distances, k_nearest)[:k_nearest]
            crowding_scores[i] = np.mean(nearest_distances)
            
        # 返回密度最高（距离最小）的个体索引
        return np.argsort(crowding_scores)[:count].tolist()
        
    def _find_worst_individuals(self, fitness_values: List[Tuple], count: int) -> List[int]:
        """
        找到适应度最差的个体
        """
        # 基于支配关系和约束违反程度排序
        indexed_fitness = [(i, f) for i, f in enumerate(fitness_values)]
        
        # 首先按约束违反程度排序（降序）
        indexed_fitness.sort(key=lambda x: x[1][2], reverse=True)
        
        # 如果存在不可行解，优先替换不可行解
        infeasible_count = sum(1 for _, f in indexed_fitness if f[2] > 0)
        if infeasible_count > 0:
            worst_count = min(count, infeasible_count)
            return [indexed_fitness[i][0] for i in range(worst_count)]
            
        # 对于可行解，使用简单的加权和排序
        feasible_fitness = [(i, f) for i, f in indexed_fitness if f[2] == 0]
        feasible_fitness.sort(key=lambda x: x[1][0] + x[1][1], reverse=True)
        
        return [feasible_fitness[i][0] for i in range(min(count, len(feasible_fitness)))]
        
    def _find_non_boundary_individuals(self, fitness_values: List[Tuple], count: int) -> List[int]:
        """
        找到非边界个体
        """
        objectives = np.array([f[:2] for f in fitness_values])
        n_solutions = objectives.shape[0]
        
        # 计算每个个体的边界特性
        boundary_scores = np.zeros(n_solutions)
        
        for obj_idx in range(objectives.shape[1]):
            obj_values = objectives[:, obj_idx]
            min_val, max_val = np.min(obj_values), np.max(obj_values)
            
            for i in range(n_solutions):
                # 距离边界的归一化距离
                dist_to_min = abs(obj_values[i] - min_val) / (max_val - min_val + 1e-10)
                dist_to_max = abs(obj_values[i] - max_val) / (max_val - min_val + 1e-10)
                boundary_scores[i] += min(dist_to_min, dist_to_max)
                
        # 返回边界特性最弱的个体（最远离边界）
        return np.argsort(boundary_scores)[-count:].tolist()
        
    def _record_injection(self, generation: int, injection_count: int, 
                         strategy: str, reason: str, stats: Dict):
        """
        记录注入历史
        """
        record = {
            'generation': generation,
            'injection_count': injection_count,
            'strategy': strategy,
            'reason': reason,
            'success': stats.get('injected_count', 0) > 0,
            'stats': stats,
            'timestamp': generation  # 使用代数作为时间戳
        }
        
        self.injection_history.append(record)
        
    def evaluate_injection_success(self, pre_injection_diversity: float, 
                                 post_injection_diversity: float,
                                 pre_injection_hypervolume: float = None,
                                 post_injection_hypervolume: float = None) -> bool:
        """
        评估注入成功率
        
        参数:
            pre_injection_diversity: 注入前多样性
            post_injection_diversity: 注入后多样性
            pre_injection_hypervolume: 注入前超体积
            post_injection_hypervolume: 注入后超体积
            
        返回:
            是否成功
        """
        # 多样性改善计算
        diversity_improvement = (post_injection_diversity - pre_injection_diversity) / (pre_injection_diversity + 1e-10)
        
        # 自适应成功阈值：根据当前多样性水平调整期望改善幅度
        if pre_injection_diversity > 100:  # 高多样性情况
            diversity_threshold = 0.01  # 1%改善即可
        elif pre_injection_diversity > 50:  # 中等多样性情况
            diversity_threshold = 0.02  # 2%改善
        else:  # 低多样性情况
            diversity_threshold = 0.05  # 5%改善
        
        success = diversity_improvement > diversity_threshold
        
        # 如果多样性没有改善，检查是否至少保持了较高水平
        if not success and pre_injection_diversity > 50:
            # 在高多样性情况下，只要多样性没有显著下降就算成功
            success = diversity_improvement > -0.02  # 允许轻微下降（2%以内）
        
        # 如果有超体积信息，也考虑超体积改善
        if pre_injection_hypervolume is not None and post_injection_hypervolume is not None:
            hv_improvement = (post_injection_hypervolume - pre_injection_hypervolume) / (pre_injection_hypervolume + 1e-10)
            success = success or hv_improvement > 0.01  # 超体积改善超过1%
            
        # 更新成功历史
        self.success_history.append(success)
        if success:
            self.successful_injections += 1
            
        # 更新注入比例（自适应调整）
        self._update_injection_ratio(success)
        
        # 更新最近的注入记录
        if self.injection_history:
            self.injection_history[-1]['success'] = success
            
        return success
        
    def _update_injection_ratio(self, success: bool):
        """
        根据成功率自适应更新注入比例
        """
        if success:
            # 成功时略微增加注入比例
            self.current_injection_ratio *= (1 + self.adaptation_learning_rate * 0.5)
        else:
            # 失败时略微减少注入比例
            self.current_injection_ratio *= (1 - self.adaptation_learning_rate * 0.3)
            
        # 保持在合理范围内
        self.current_injection_ratio = max(
            self.min_injection_ratio, 
            min(self.current_injection_ratio, self.max_injection_ratio)
        )
        
    def get_injection_statistics(self) -> Dict[str, Any]:
        """
        获取注入统计信息
        """
        success_rate = (self.successful_injections / self.total_injections 
                       if self.total_injections > 0 else 0.0)
        
        recent_success_rate = (sum(self.success_history) / len(self.success_history) 
                              if self.success_history else 0.0)
        
        strategy_stats = {}
        for record in self.injection_history:
            strategy = record['strategy']
            if strategy not in strategy_stats:
                strategy_stats[strategy] = {'count': 0, 'success': 0}
            strategy_stats[strategy]['count'] += 1
            if record.get('success', False):
                strategy_stats[strategy]['success'] += 1
                
        return {
            'total_injections': self.total_injections,
            'successful_injections': self.successful_injections,
            'overall_success_rate': success_rate,
            'recent_success_rate': recent_success_rate,
            'current_injection_ratio': self.current_injection_ratio,
            'strategy_statistics': strategy_stats,
            'injection_history_length': len(self.injection_history)
        }
        
    def reset_statistics(self):
        """重置统计信息"""
        self.total_injections = 0
        self.successful_injections = 0
        self.injection_history.clear()
        self.success_history.clear()
        
    # 兼容性方法，用于支持测试用例
    def calculate_injection_count(self, population_size: int, diversity_monitor, 
                                injection_reason: str = "manual") -> int:
        """
        计算注入数量（兼容性方法）
        
        参数:
            population_size: 种群大小
            diversity_monitor: 多样性监控器
            injection_reason: 注入原因
            
        返回:
            注入数量
        """
        return self._determine_injection_count(population_size, diversity_monitor, injection_reason)
    
    def select_injection_strategy(self, diversity_monitor, injection_reason: str = "manual") -> str:
        """
        选择注入策略（兼容性方法）
        
        参数:
            diversity_monitor: 多样性监控器
            injection_reason: 注入原因
            
        返回:
            策略名称
        """
        return self._select_injection_strategy(diversity_monitor, injection_reason)
    
    def update_adaptation(self, success: bool):
        """
        更新自适应参数（兼容性方法）
        
        参数:
            success: 是否成功
        """
        self._update_injection_ratio(success)
        if success:
            self.successful_injections += 1
        
    def inject_knowledge_simple(self, population: List[Dict], fitness_values: List[Tuple], 
                               knowledge_base, injection_count: int = None) -> Tuple[List[Dict], List[Tuple]]:
        """
        简化的知识注入方法（兼容性方法）
        
        参数:
            population: 当前种群
            fitness_values: 种群适应度值
            knowledge_base: 动态知识库
            injection_count: 注入数量
            
        返回:
            (新种群, 新适应度值)
        """
        if injection_count is None:
            injection_count = max(1, int(len(population) * self.current_injection_ratio))
            
        # 创建一个简单的多样性监控器
        class SimpleDiversityMonitor:
            def get_current_diversity(self):
                return 0.5
            @property
            def diversity_threshold(self):
                return 0.3
        
        dummy_monitor = SimpleDiversityMonitor()
        
        new_population, new_fitness, _ = self.inject_knowledge(
            population, fitness_values, knowledge_base, dummy_monitor, 
            0, "manual")
        
        return new_population, new_fitness
        
    def get_success_rate(self) -> float:
        """
        获取注入成功率（兼容性方法）
        
        返回:
            成功率
        """
        if self.total_injections == 0:
            return 0.0
        return self.successful_injections / self.total_injections
        
    def get_current_injection_ratio(self) -> float:
        """
        获取当前注入比例（兼容性方法）
        
        返回:
            当前注入比例
        """
        return self.current_injection_ratio 