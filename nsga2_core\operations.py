"""
NSGA-II遗传操作模块 - 实现交叉和变异操作

本模块实现了NSGA-II算法中的交叉和变异操作，包括:
1. 模拟二进制交叉(SBX)
2. 多项式变异(PM)
3. 种群级别的交叉和变异操作
"""

import random
from deap import tools
import copy

# 设置随机种子以确保结果可重现
random.seed(42)


def simulated_binary_crossover(parent1, parent2, eta=20.0, node_bounds=(-1e3, 1e3), weight_bounds=(0.0, 1.0)):
    """
    执行模拟二进制交叉(SBX)操作

    对两个父代个体的路径编码进行交叉操作，生成两个子代个体。
    分别对节点编码(M1)和权重编码(M2)进行SBX交叉。

    参数:
        parent1: 第一个父代个体
        parent2: 第二个父代个体
        eta: 分布指数，控制子代与父代的相似程度
        node_bounds: 节点编码的取值范围
        weight_bounds: 权重编码的取值范围

    返回:
        offspring1, offspring2: 两个子代个体
    """
    # 创建子代的浅拷贝
    offspring1 = parent1.copy()
    offspring2 = parent2.copy()

    # 对每个飞机的路径进行交叉
    for aircraft_id in parent1.keys():
        # 提取父代的路径数据
        path1 = parent1[aircraft_id]
        path2 = parent2[aircraft_id]

        # 重置滑行起始时间为0（将在评估时调整）
        new_start_time1 = 0
        new_start_time2 = 0

        # 分离节点编码(M1)和权重编码(M2)
        nodes1 = [item[0] for item in path1[1:] if isinstance(item, tuple)]
        nodes2 = [item[0] for item in path2[1:] if isinstance(item, tuple)]
        weights1 = [item[1] for item in path1[1:] if isinstance(item, tuple)]
        weights2 = [item[1] for item in path2[1:] if isinstance(item, tuple)]

        # 对节点编码执行SBX交叉
        new_nodes1, new_nodes2 = [], []
        for n1, n2 in zip(nodes1, nodes2):
            # 使用DEAP库的SBX交叉操作
            n1_new, n2_new = tools.cxSimulatedBinaryBounded(
                [n1], [n2], eta=eta, low=node_bounds[0], up=node_bounds[1]
            )
            new_nodes1.append(n1_new[0])
            new_nodes2.append(n2_new[0])

        # 对权重编码执行SBX交叉
        new_weights1, new_weights2 = [], []
        for w1, w2 in zip(weights1, weights2):
            # 使用DEAP库的SBX交叉操作
            w1_new, w2_new = tools.cxSimulatedBinaryBounded(
                [w1], [w2], eta=eta, low=weight_bounds[0], up=weight_bounds[1]
            )
            new_weights1.append(w1_new[0])
            new_weights2.append(w2_new[0])

        # 重组路径编码
        offspring1[aircraft_id] = [new_start_time1] + [(n, w) for n, w in zip(new_nodes1, new_weights1)]
        offspring2[aircraft_id] = [new_start_time2] + [(n, w) for n, w in zip(new_nodes2, new_weights2)]

    return offspring1, offspring2


def polynomial_mutation(individual, mutation_prob, eta=20.0, node_bounds=(-1e3, 1e3), weight_bounds=(0.0, 1.0)):
    """
    执行多项式变异(PM)操作

    对个体的路径编码进行变异操作。
    分别对节点编码(M1)和权重编码(M2)进行多项式变异。

    参数:
        individual: 待变异的个体
        mutation_prob: 变异概率
        eta: 分布指数，控制变异后的值与原值的相似程度
        node_bounds: 节点编码的取值范围
        weight_bounds: 权重编码的取值范围

    返回:
        mutated_individual: 变异后的个体
    """
    # 对每个飞机的路径进行变异
    for aircraft_id in individual.keys():
        # 提取路径数据
        path = individual[aircraft_id]

        # 重置滑行起始时间为0（将在评估时调整）
        mutated_start_time = 0

        # 分离节点编码(M1)和权重编码(M2)
        nodes = [item[0] for item in path[1:] if isinstance(item, tuple)]
        weights = [item[1] for item in path[1:] if isinstance(item, tuple)]

        # 对节点编码执行多项式变异
        mutated_nodes = []
        for node_value in nodes:
            # 使用DEAP库的多项式变异操作
            mutated_value = tools.mutPolynomialBounded(
                [node_value],
                eta=eta,
                low=node_bounds[0],
                up=node_bounds[1],
                indpb=mutation_prob
            )[0][0]
            mutated_nodes.append(mutated_value)

        # 对权重编码执行多项式变异
        mutated_weights = []
        for weight_value in weights:
            # 使用DEAP库的多项式变异操作
            mutated_value = tools.mutPolynomialBounded(
                [weight_value],
                eta=eta,
                low=weight_bounds[0],
                up=weight_bounds[1],
                indpb=mutation_prob
            )[0][0]
            mutated_weights.append(mutated_value)

        # 重组路径编码
        mutated_path = [mutated_start_time] + [(n, w) for n, w in zip(mutated_nodes, mutated_weights)]
        individual[aircraft_id] = mutated_path

    return individual


def crossover_and_mutate(population, crossover_prob, mutation_prob, crossover_eta=20.0, mutation_eta=20.0):
    """
    对种群执行交叉和变异操作

    将种群中的个体两两配对进行交叉，然后对子代进行变异，生成新的种群。

    参数:
        population: 当前种群
        crossover_prob: 交叉概率
        mutation_prob: 变异概率
        crossover_eta: 交叉分布指数
        mutation_eta: 变异分布指数

    返回:
        offspring_population: 子代种群
    """
    offspring_population = []

    # 按种群对进行交叉（每次取两个个体）
    for i in range(0, len(population) - 1, 2):
        # 创建父代的深拷贝，避免修改原始个体
        parent1 = copy.deepcopy(population[i])
        parent2 = copy.deepcopy(population[i + 1])

        # 根据交叉概率决定是否进行交叉
        if random.random() < crossover_prob:
            # 执行模拟二进制交叉
            offspring1, offspring2 = simulated_binary_crossover(
                parent1, parent2, eta=crossover_eta
            )
        else:
            # 不进行交叉，直接复制父代
            offspring1, offspring2 = parent1, parent2

        # 对子代执行多项式变异
        offspring1 = polynomial_mutation(offspring1, mutation_prob, eta=mutation_eta)
        offspring2 = polynomial_mutation(offspring2, mutation_prob, eta=mutation_eta)

        # 将子代添加到新种群
        offspring_population.append(offspring1)
        offspring_population.append(offspring2)

    # 如果种群大小为奇数，将最后一个个体直接复制到子代
    if len(population) % 2 == 1:
        offspring_population.append(copy.deepcopy(population[-1]))

    return offspring_population
