#!/usr/bin/env python3
"""
测试改进后的知识库初始化逻辑

这个脚本用于验证：
1. 使用单目标优化预热初始化时，知识库包含高质量解
2. 种群保持随机多样性
3. 避免知识库和种群内容重复的问题
"""

import pandas as pd
import networkx as nx
import numpy as np
import os
import sys
import logging
from nsga2_core.algorithm import MARMT_RK_global_with_akid
from config import get_akid_config

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_test_data():
    """加载测试数据"""
    print("正在加载测试数据...")
    
    # 读取机场布局数据
    airport_data_path = 'doh1.txt'
    if not os.path.exists(airport_data_path):
        print(f"错误：找不到数据文件 {airport_data_path}")
        return None
        
    try:
        # 使用main_global.py中的函数加载数据
        from main_global import load_airport_data, create_airport_graph
        nodes_df, edges_df, aircraft_df = load_airport_data(airport_data_path)
        
        # 读取速度配置数据
        speed_profile_path = 'doh1_database.csv'
        speed_profile_df = pd.read_csv(speed_profile_path)
        
        # 创建机场图
        airport_graph = create_airport_graph(edges_df)
        
        print(f"✅ 数据加载完成 - 节点:{len(nodes_df)}, 边:{len(edges_df)}, 飞机:{len(aircraft_df)}")
        return nodes_df, edges_df, aircraft_df, speed_profile_df, airport_graph
        
    except Exception as e:
        print(f"数据加载失败: {e}")
        return None

def test_traditional_initialization():
    """测试传统初始化方式"""
    print("\n" + "="*60)
    print("测试传统初始化方式（知识库从种群获取）")
    print("="*60)
    
    data = load_test_data()
    if data is None:
        return None
        
    nodes_df, edges_df, aircraft_df, speed_profile_df, airport_graph = data
    
    # 获取AKID配置
    akid_config = get_akid_config('default')
    
    # 创建输出文件夹
    output_folder = 'results/test_traditional/'
    os.makedirs(output_folder, exist_ok=True)
    
    try:
        # 运行传统初始化
        pareto_front, pareto_set, akid_report = MARMT_RK_global_with_akid(
            aircraft_df, airport_graph, nodes_df, edges_df, speed_profile_df,
            run_index=1, output_folder=output_folder,
            use_akid=True, akid_config=akid_config,
            use_warmup_initialization=False,  # 使用传统随机初始化
            warmup_config=None
        )
        
        print(f"传统初始化测试完成：")
        print(f"  帕累托前沿大小: {len(pareto_front)}")
        if akid_report:
            kb_stats = akid_report.get('knowledge_base_stats', {})
            print(f"  知识库大小: {kb_stats.get('size', 'N/A')}")
            print(f"  知识库容量: {kb_stats.get('capacity', 'N/A')}")
        
        return akid_report
        
    except Exception as e:
        print(f"传统初始化测试失败: {e}")
        return None

def test_improved_initialization():
    """测试改进后的初始化方式"""
    print("\n" + "="*60)
    print("测试改进初始化方式（知识库从单目标优化获取）")
    print("="*60)
    
    data = load_test_data()
    if data is None:
        return None
        
    nodes_df, edges_df, aircraft_df, speed_profile_df, airport_graph = data
    
    # 获取AKID配置
    akid_config = get_akid_config('default')
    
    # 预热配置
    warmup_config = {
        'warmup_generations': 10,  # 减少代数以加快测试
        'warmup_population_size': 30,
        'elite_count_per_objective': 20
    }
    
    # 创建输出文件夹
    output_folder = 'results/test_improved/'
    os.makedirs(output_folder, exist_ok=True)
    
    try:
        # 运行改进后的初始化
        pareto_front, pareto_set, akid_report = MARMT_RK_global_with_akid(
            aircraft_df, airport_graph, nodes_df, edges_df, speed_profile_df,
            run_index=1, output_folder=output_folder,
            use_akid=True, akid_config=akid_config,
            use_warmup_initialization=True,  # 使用单目标优化预热初始化
            warmup_config=warmup_config
        )
        
        print(f"改进初始化测试完成：")
        print(f"  帕累托前沿大小: {len(pareto_front)}")
        if akid_report:
            kb_stats = akid_report.get('knowledge_base_stats', {})
            injection_stats = akid_report.get('injection_stats', {})
            print(f"  知识库大小: {kb_stats.get('size', 'N/A')}")
            print(f"  知识库容量: {kb_stats.get('capacity', 'N/A')}")
            print(f"  总注入次数: {injection_stats.get('total_injections', 'N/A')}")
            print(f"  成功注入次数: {injection_stats.get('successful_injections', 'N/A')}")
        
        return akid_report
        
    except Exception as e:
        print(f"改进初始化测试失败: {e}")
        return None

def compare_results(traditional_report, improved_report):
    """比较两种方法的结果"""
    print("\n" + "="*60)
    print("结果对比分析")
    print("="*60)
    
    if traditional_report is None or improved_report is None:
        print("无法进行对比：某个测试失败")
        return
    
    print("传统方式 vs 改进方式：")
    
    # 知识库对比
    trad_kb = traditional_report.get('knowledge_base_stats', {})
    impr_kb = improved_report.get('knowledge_base_stats', {})
    
    print(f"\n知识库统计：")
    print(f"  传统方式知识库大小: {trad_kb.get('size', 'N/A')}")
    print(f"  改进方式知识库大小: {impr_kb.get('size', 'N/A')}")
    
    # 注入统计对比
    trad_injection = traditional_report.get('injection_stats', {})
    impr_injection = improved_report.get('injection_stats', {})
    
    print(f"\n知识注入统计：")
    print(f"  传统方式总注入次数: {trad_injection.get('total_injections', 'N/A')}")
    print(f"  改进方式总注入次数: {impr_injection.get('total_injections', 'N/A')}")
    print(f"  传统方式成功率: {trad_injection.get('success_rate', 'N/A')}")
    print(f"  改进方式成功率: {impr_injection.get('success_rate', 'N/A')}")
    
    # 多样性对比
    trad_diversity = traditional_report.get('diversity_monitor_stats', {})
    impr_diversity = improved_report.get('diversity_monitor_stats', {})
    
    print(f"\n多样性统计：")
    print(f"  传统方式最终多样性: {trad_diversity.get('current_diversity', 'N/A')}")
    print(f"  改进方式最终多样性: {impr_diversity.get('current_diversity', 'N/A')}")

def main():
    """主测试函数"""
    print("🧪 AKID-NSGA2 知识库初始化改进测试")
    print("="*80)
    
    print("\n测试说明：")
    print("1. 传统方式：知识库从随机初始化的种群获取解")
    print("2. 改进方式：知识库从单目标优化结果获取高质量解，种群全随机")
    print("3. 预期：改进方式应该有更好的注入成功率和多样性")
    
    # 测试传统初始化
    traditional_report = test_traditional_initialization()
    
    # 测试改进后的初始化
    improved_report = test_improved_initialization()
    
    # 对比结果
    compare_results(traditional_report, improved_report)
    
    print("\n" + "="*80)
    print("测试完成！")
    
    if traditional_report and improved_report:
        print("✅ 两种方法都成功运行")
        print("📊 请查看上面的对比结果")
    else:
        print("❌ 某些测试失败，请检查日志")

if __name__ == "__main__":
    main() 