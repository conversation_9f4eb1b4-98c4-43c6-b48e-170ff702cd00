<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="28164097-2b25-4f9b-8253-eed631723305" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="FlaskConsoleOptions" custom-start-script="import sys; print('Python %s on %s' % (sys.version, sys.platform)); sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo, NoAppException&#10;for module in [&quot;main.py&quot;, &quot;wsgi.py&quot;, &quot;app.py&quot;]:&#10;    try: locals().update(ScriptInfo(app_import_path=module, create_app=None).load_app().make_shell_context()); print(&quot;\nFlask App: %s&quot; % app.import_name); break&#10;    except NoAppException: pass">
    <envs>
      <env key="FLASK_APP" value="app" />
    </envs>
    <option name="myCustomStartScript" value="import sys; print('Python %s on %s' % (sys.version, sys.platform)); sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo, NoAppException&#10;for module in [&quot;main.py&quot;, &quot;wsgi.py&quot;, &quot;app.py&quot;]:&#10;    try: locals().update(ScriptInfo(app_import_path=module, create_app=None).load_app().make_shell_context()); print(&quot;\nFlask App: %s&quot; % app.import_name); break&#10;    except NoAppException: pass" />
    <option name="myEnvs">
      <map>
        <entry key="FLASK_APP" value="app" />
      </map>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2xJH97aerWUR27mvf3WSRaxagmR" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
    <option name="showMembers" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;Python 测试.Python 测试 (test_refactored_code.py 内).executor&quot;: &quot;Run&quot;,
    &quot;Python.CalFitness_globalV2.executor&quot;: &quot;Run&quot;,
    &quot;Python.config.executor&quot;: &quot;Run&quot;,
    &quot;Python.demo_refactored_usage.executor&quot;: &quot;Run&quot;,
    &quot;Python.main.executor&quot;: &quot;Run&quot;,
    &quot;Python.main_global.executor&quot;: &quot;Run&quot;,
    &quot;Python.main_global_enhanced.executor&quot;: &quot;Run&quot;,
    &quot;Python.performance_comparison.executor&quot;: &quot;Run&quot;,
    &quot;Python.quick_check.executor&quot;: &quot;Run&quot;,
    &quot;Python.run_demo.executor&quot;: &quot;Run&quot;,
    &quot;Python.run_simple.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_akid_nsga2.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_akid_performance_comparison.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_improved_warmup.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_new_strategy.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_transfer_learning_phase1.executor&quot;: &quot;Run&quot;,
    &quot;Python.time_window_calculator.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowPanel.toolwindow.highlight.mappings&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowPanel.toolwindow.highlight.symlinks&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowPanel.toolwindow.show.date&quot;: &quot;false&quot;,
    &quot;WebServerToolWindowPanel.toolwindow.show.permissions&quot;: &quot;false&quot;,
    &quot;WebServerToolWindowPanel.toolwindow.show.size&quot;: &quot;false&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/AGM_NSGA2/data&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;web.server&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\AGM_NSGA2\data" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-PY-251.25410.159" />
        <option value="bundled-python-sdk-e0ed3721d81e-36ea0e71a18c-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.25410.159" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="28164097-2b25-4f9b-8253-eed631723305" name="更改" comment="" />
      <created>1747649103776</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1747649103776</updated>
      <workItem from="1747649106065" duration="1039000" />
      <workItem from="1747668888662" duration="244000" />
      <workItem from="1747730755173" duration="5025000" />
      <workItem from="1747753736608" duration="602000" />
      <workItem from="1747789853239" duration="1232000" />
      <workItem from="1747895465285" duration="1029000" />
      <workItem from="1747930507029" duration="925000" />
      <workItem from="1747955873572" duration="595000" />
      <workItem from="1747975653454" duration="2155000" />
      <workItem from="1748083730144" duration="3591000" />
      <workItem from="1748104084162" duration="51000" />
      <workItem from="1748107052935" duration="5000" />
      <workItem from="1748270093727" duration="1355000" />
      <workItem from="1748327263113" duration="1224000" />
      <workItem from="1748328533419" duration="11625000" />
      <workItem from="1748358796502" duration="21000" />
      <workItem from="1748359112622" duration="573000" />
      <workItem from="1748416136992" duration="511000" />
      <workItem from="1748416687069" duration="2658000" />
      <workItem from="1748428286610" duration="46000" />
      <workItem from="1748439095848" duration="59000" />
      <workItem from="1748449824842" duration="2323000" />
      <workItem from="1748522207314" duration="1819000" />
      <workItem from="1748537328511" duration="195000" />
      <workItem from="1748537826283" duration="32000" />
      <workItem from="1748567503660" duration="20000" />
      <workItem from="1748768687672" duration="4277000" />
      <workItem from="1748861633376" duration="6806000" />
      <workItem from="1748882843861" duration="596000" />
      <workItem from="1748937423293" duration="4811000" />
      <workItem from="1748971182966" duration="7000" />
      <workItem from="1749100330437" duration="12085000" />
      <workItem from="1749145046881" duration="90000" />
      <workItem from="1749300657931" duration="1955000" />
      <workItem from="1749318771367" duration="326000" />
      <workItem from="1749456174516" duration="554000" />
      <workItem from="1749492291313" duration="27000" />
      <workItem from="1749541902800" duration="4047000" />
      <workItem from="1749546761317" duration="1257000" />
      <workItem from="1749571380795" duration="733000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/AGM_NSGA2$test_akid_performance_comparison.coverage" NAME="test_akid_performance_comparison 覆盖结果" MODIFIED="1748940347902" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/AGM_NSGA2$CalFitness_globalV2.coverage" NAME="CalFitness_globalV2 覆盖结果" MODIFIED="1748865763329" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/AGM_NSGA2$run_demo.coverage" NAME="run_demo 覆盖结果" MODIFIED="1748946282146" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/AGM_NSGA2$test_akid_nsga2.coverage" NAME="test_akid_nsga2 覆盖结果" MODIFIED="1748522841297" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/AGM_NSGA2$main_global_enhanced.coverage" NAME="main_global_enhanced 覆盖结果" MODIFIED="1748492691747" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/AGM_NSGA2$config.coverage" NAME="config 覆盖结果" MODIFIED="1747649465016" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src/config" />
    <SUITE FILE_PATH="coverage/AGM_NSGA2$run_simple.coverage" NAME="run_simple 覆盖结果" MODIFIED="1749542933920" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/AGM_NSGA2$test_new_strategy.coverage" NAME="test_new_strategy 覆盖结果" MODIFIED="1749117571910" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/AGM_NSGA2$main_global.coverage" NAME="main_global 覆盖结果" MODIFIED="1749545602347" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/AGM_NSGA2$.coverage" NAME=" 覆盖结果" MODIFIED="1747895545839" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/AGM_NSGA2$time_window_calculator.coverage" NAME="time_window_calculator 覆盖结果" MODIFIED="1747895567421" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/AGM_NSGA2$test_improved_warmup.coverage" NAME="test_improved_warmup 覆盖结果" MODIFIED="1748862847207" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/AGM_NSGA2$quick_check.coverage" NAME="quick_check 覆盖结果" MODIFIED="1748946259859" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/AGM_NSGA2$performance_comparison.coverage" NAME="performance_comparison 覆盖结果" MODIFIED="1747895640442" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/AGM_NSGA2$main.coverage" NAME="main 覆盖结果" MODIFIED="1748944317250" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/AGM_NSGA2$test_transfer_learning_phase1.coverage" NAME="test_transfer_learning_phase1 覆盖结果" MODIFIED="1749544747516" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/AGM_NSGA2$demo_refactored_usage.coverage" NAME="demo_refactored_usage 覆盖结果" MODIFIED="1747895621853" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>